
use std::ffi::c_void;
use std::fs::{File, OpenOptions};
use std::io::Write;
use std::time::{SystemTime, UNIX_EPOCH};

// Simple logger implementation
pub struct Logger {
    pub log_file: Option<File>,
    pub debug_level: u8,
}

impl Logger {
    // Initialize the logger
    pub unsafe fn init(&mut self, filename: &str) {
        match OpenOptions::new()
            .create(true)
            .write(true)
            .truncate(true)
            .open(filename)
        {
            Ok(file) => {
                self.log_file = Some(file);
                self.log("Logger initialized successfully", 2);
            }
            Err(e) => {
                println!("Failed to open log file: {}", e);
                self.log_file = None;
            }
        }
    }

    // Log a message with a specific level
    pub unsafe fn log(&mut self, message: &str, level: u8) {
        if level <= self.debug_level {
            let level_prefix = match level {
                1 => "[ERROR] ",
                2 => "[INFO] ",
                3 => "[DEBUG] ",
                4 => "[TRACE] ",
                _ => "[UNKNOWN] ",
            };

            // Get current time as seconds since epoch with microsecond precision
            let timestamp = match SystemTime::now().duration_since(UNIX_EPOCH) {
                Ok(n) => n.as_secs_f64(),
                Err(_) => 0.0,
            };
            let log_message = format!("[{:.6}] {} {}\n", timestamp, level_prefix, message);

            // Print to console
            print!("{}", log_message);

            // Write to file if available
            if let Some(file) = &mut self.log_file {
                let _ = file.write_all(log_message.as_bytes());
                let _ = file.flush();
            }
        }
    }

    // Get current timestamp for performance measurement
    pub unsafe fn get_timestamp(&self) -> f64 {
        match SystemTime::now().duration_since(UNIX_EPOCH) {
            Ok(n) => n.as_secs_f64(),
            Err(_) => 0.0,
        }
    }

    // Log start of an operation with timestamp
    pub unsafe fn start_operation(&mut self, operation_name: &str) -> f64 {
        let timestamp = self.get_timestamp();
        self.log(
            &format!(
                "START OPERATION: {} (timestamp: {:.6})",
                operation_name, timestamp
            ),
            3,
        );
        timestamp
    }

    // Log end of an operation with duration
    pub unsafe fn end_operation(&mut self, operation_name: &str, start_timestamp: f64) {
        let end_timestamp = self.get_timestamp();
        let duration = end_timestamp - start_timestamp;
        self.log(
            &format!(
                "END OPERATION: {} (duration: {:.6} seconds)",
                operation_name, duration
            ),
            3,
        );
    }

    // Log memory address with detailed information
    pub unsafe fn log_address(&mut self, name: &str, address: *mut c_void) {
        self.log(
            &format!(
                "MEMORY ADDRESS: {} at {:p} (decimal: {})",
                name, address, address as usize
            ),
            3,
        );
    }

    // Log function call parameters
    pub unsafe fn log_function_call(&mut self, function_name: &str, params: &[(&str, String)]) {
        let mut params_str = String::new();
        for (i, (name, value)) in params.iter().enumerate() {
            if i > 0 {
                params_str.push_str(", ");
            }
            params_str.push_str(&format!("{}: {}", name, value));
        }
        self.log(&format!("FUNCTION CALL: {}({})", function_name, params_str), 3);
    }

    // Log stack state - simplified version to reduce stack usage
    pub unsafe fn log_stack_state(&mut self, prefix: &str) {
        let rsp: usize;
        std::arch::asm!("mov {}, rsp", out(reg) rsp);

        self.log(&format!("STACK STATE {}: RSP = 0x{:016X}", prefix, rsp), 3);

        // Only log stack pointer, not stack contents, to reduce stack usage
        // This prevents stack overflow in recursive or frequent calls
    }

    // Log register state - extremely simplified version to prevent stack overflow
    pub unsafe fn log_register_state(&mut self, prefix: &str) {
        // Only log RSP and RIP to minimize stack usage
        let rsp: usize;
        let rip: usize;

        std::arch::asm!(
            "mov {rsp}, rsp",
            "lea {rip}, [rip]",
            rsp = out(reg) rsp,
            rip = out(reg) rip,
        );

        self.log(
            &format!("REGISTER STATE {}: RSP=0x{:016X} RIP=0x{:016X}", prefix, rsp, rip),
            3,
        );

        // Skip logging all other registers to reduce stack usage
        // This prevents stack overflow in recursive or frequent calls
    }

    // Convenience methods for different log levels
    pub unsafe fn error(&mut self, message: &str) {
        self.log(message, 1);
    }

    pub unsafe fn warn(&mut self, message: &str) {
        self.log(&format!("WARNING: {}", message), 1);
    }

    pub unsafe fn info(&mut self, message: &str) {
        self.log(message, 2);
    }

    pub unsafe fn debug(&mut self, message: &str) {
        self.log(message, 3);
    }

    pub unsafe fn trace(&mut self, message: &str) {
        self.log(message, 4);
    }
}

