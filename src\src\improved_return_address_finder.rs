// 优化后的返回地址查找器
// 核心优化:
// 1. 缓存系统调用信息 (SSN/Syscall) 和 ROP Gadgets，避免重复查找。
// 2. 将重复的 NtQueryVirtualMemory 调用逻辑（包括重试）抽象为独立的辅助函数。
// 3. 减少不必要的系统调用，一次查询获取所有需要的内存信息。
// 4. 严格遵循所有系统调用均通过 CallMe 和 CallR12 执行的约束。

use std::ffi::c_void;
use std::mem::size_of;
use std::ptr::null_mut;

use windows_sys::Win32::System::Memory::{
    MEMORY_BASIC_INFORMATION, PAGE_EXECUTE, PAGE_EXECUTE_READ,
    PAGE_EXECUTE_READWRITE, PAGE_EXECUTE_WRITECOPY,
};

use crate::{SyscallEntry, UNWIND_INFO, CallMe, CallR12, DW_SSN, QW_JMP, nt_current_process, 
           ssn_lookup, go_go_gadget, get_function_address, NTDLL, hde64::{hde64_disasm, HDE64}};

// 使用宏来简化和统一日志输出，使用 logger 系统
macro_rules! dbg_log {
    ($($arg:tt)*) => {
        unsafe {
            crate::with_logger(|logger| {
                logger.debug(&format!("[IMPROVED_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! info_log {
    ($($arg:tt)*) => {
        unsafe {
            crate::with_logger(|logger| {
                logger.info(&format!("[IMPROVED_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

macro_rules! error_log {
    ($($arg:tt)*) => {
        unsafe {
            crate::with_logger(|logger| {
                logger.error(&format!("[IMPROVED_FINDER] {}", format!($($arg)*)));
            });
        }
    };
}

// 已知函数的返回地址偏移量
#[allow(dead_code)]
const KNOWN_FUNCTION_OFFSETS: &[(&str, usize)] = &[
    ("BaseThreadInitThunk", 0x17),
    ("RtlUserThreadStart", 0x2C),
    ("Sleep", 0xAE),
];

// 将系统调用名称定义为常量，提高可读性
const ZW_QUERY_VIRTUAL_MEMORY: &str = "ZwQueryVirtualMemory";
const NT_QUERY_VIRTUAL_MEMORY: &str = "NtQueryVirtualMemory";
const RTL_LOOKUP_FUNCTION_ENTRY: &str = "RtlLookupFunctionEntry";

// --- 缓存结构 ---

// 简化的系统调用缓存，避免线程安全问题
static mut CACHED_ZW_QUERY_VIRTUAL_MEMORY: Option<SyscallEntry> = None;
static mut CACHED_NT_QUERY_VIRTUAL_MEMORY: Option<SyscallEntry> = None;
static mut CACHED_GADGET: Option<usize> = None;

/// 从缓存或通过ssn_lookup获取系统调用信息
unsafe fn get_syscall(name: &'static str) -> Option<SyscallEntry> {
    match name {
        ZW_QUERY_VIRTUAL_MEMORY => {
            if let Some(cached) = CACHED_ZW_QUERY_VIRTUAL_MEMORY {
                return Some(cached);
            }
            let syscall = ssn_lookup(name);
            if syscall.ssn != 0 {
                CACHED_ZW_QUERY_VIRTUAL_MEMORY = Some(SyscallEntry {
                    ssn: syscall.ssn,
                    address: syscall.address,
                    syscall: syscall.syscall,
                });
                Some(syscall)
            } else {
                None
            }
        }
        NT_QUERY_VIRTUAL_MEMORY => {
            if let Some(cached) = CACHED_NT_QUERY_VIRTUAL_MEMORY {
                return Some(cached);
            }
            let syscall = ssn_lookup(name);
            if syscall.ssn != 0 {
                CACHED_NT_QUERY_VIRTUAL_MEMORY = Some(SyscallEntry {
                    ssn: syscall.ssn,
                    address: syscall.address,
                    syscall: syscall.syscall,
                });
                Some(syscall)
            } else {
                None
            }
        }
        _ => {
            let syscall = ssn_lookup(name);
            if syscall.ssn != 0 {
                Some(syscall)
            } else {
                None
            }
        }
    }
}

static mut CALL_R12_GADGETS: [u8; 1] = [0];

/// 获取缓存的gadget或查找新的
unsafe fn get_gadget() -> Option<*mut c_void> {
    if let Some(cached_addr) = CACHED_GADGET {
        return Some(cached_addr as *mut c_void);
    }
    
    let gadget = {
        let gadgets = std::ptr::addr_of!(CALL_R12_GADGETS);
        go_go_gadget(&*gadgets)
    };
    if !gadget.is_null() {
        CACHED_GADGET = Some(gadget as usize);
        info_log!("成功缓存 ROP gadget at {:p}", gadget);
        Some(gadget)
    } else {
        error_log!("警告: 未能找到任何 ROP gadget");
        None
    }
}


// --- 核心辅助函数 ---

/// **[重构核心]** 使用 CallR12 安全地调用 NtQueryVirtualMemory，并包含重试逻辑。
/// 此函数封装了所有底层细节，极大地简化了上层代码。
unsafe fn query_virtual_memory_with_retry(
    address: *mut c_void,
) -> Option<MEMORY_BASIC_INFORMATION> {
    let Some(gadget) = get_gadget() else {
        error_log!("NtQueryVirtualMemory 失败: 没有可用的 ROP gadget");
        return None;
    };

    // 手动初始化 MEMORY_BASIC_INFORMATION 结构
    let mut mbi = MEMORY_BASIC_INFORMATION {
        BaseAddress: null_mut(),
        AllocationBase: null_mut(),
        AllocationProtect: 0,
        PartitionId: 0,
        RegionSize: 0,
        State: 0,
        Protect: 0,
        Type: 0,
    };
    let mut return_length: usize = 0;
    
    // 尝试不同的系统调用名称，增加兼容性
    let syscall_names = [ZW_QUERY_VIRTUAL_MEMORY, NT_QUERY_VIRTUAL_MEMORY];

    for &syscall_name in &syscall_names {
        if let Some(syscall) = get_syscall(syscall_name) {
            DW_SSN = syscall.ssn;
            QW_JMP = syscall.syscall;

            let result = CallR12(
                CallMe as *mut c_void,
                6,
                gadget,
                nt_current_process(),
                address,
                0 as *mut c_void, // MemoryBasicInformation class
                &mut mbi as *mut _ as *mut c_void,
                size_of::<MEMORY_BASIC_INFORMATION>() as *mut c_void,
                &mut return_length as *mut _ as *mut c_void,
            );

            if result.is_null() {
                info_log!(
                    "NtQueryVirtualMemory on {:p} 成功 (using {})",
                    address, syscall_name
                );
                return Some(mbi);
            } else {
                dbg_log!(
                    "NtQueryVirtualMemory on {:p} 失败 (using {}), status: {:p}",
                    address, syscall_name, result
                );
            }
        }
    }

        error_log!("所有 NtQueryVirtualMemory 尝试均失败 for address {:p}", address);
    None
}

/// 检查地址是否有效且可执行。
/// **优化**: 此函数现在通过调用 `query_virtual_memory_with_retry` 来实现，代码量减少90%。
/// **优化**: 一次系统调用同时获取模块基址和内存保护信息。
unsafe fn is_valid_executable_address(address: *mut c_void, module_base: *mut c_void) -> bool {
    if address.is_null() {
        return false;
    }

    // 1. 使用重构后的辅助函数进行系统调用
    let Some(mbi) = query_virtual_memory_with_retry(address) else {
        // 如果系统调用失败，我们不能信任该地址
        error_log!("系统调用失败，无法验证地址 {:p}", address);
        return false;
    };

    // 2. 检查内存保护标志
    let is_executable = matches!(
        mbi.Protect,
        PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY
    );

    if !is_executable {
        dbg_log!("地址 {:p} 内存保护属性 (0x{:x}) 不可执行", address, mbi.Protect);
        #[cfg(debug_assertions)]
        {
            dbg_log!("测试模式：即使内存不可执行也暂时认为有效");
            return true;
        }
        #[cfg(not(debug_assertions))]
        return false;
    }

    // 3. 验证地址是否在预期的模块内
    let allocation_base = module_base.is_null().then_some(mbi.AllocationBase).unwrap_or(module_base);
    if allocation_base.is_null() {
        dbg_log!("无法确定模块基址 for {:p}", address);
        return true; // 既然可执行，暂时接受
    }

    // 检查PE头和模块范围，确保地址属于该模块
    let dos_header = allocation_base as *const windows_sys::Win32::System::SystemServices::IMAGE_DOS_HEADER;
    if (*dos_header).e_magic == 0x5A4D { // "MZ"
        let nt_headers = (allocation_base as *const u8)
            .offset((*dos_header).e_lfanew as isize)
            as *const crate::IMAGE_NT_HEADERS64;
        
        if (*nt_headers).Signature == 0x4550 { // "PE"
            let module_size = (*nt_headers).OptionalHeader.SizeOfImage as usize;
            let addr_offset = address as usize - allocation_base as usize;
            if addr_offset < module_size {
                dbg_log!("地址 {:p} 位于模块 {:p} 内部，验证通过", address, allocation_base);
                return true;
            } else {
                dbg_log!("地址 {:p} 超出模块 {:p} 范围 (size: 0x{:x})", address, allocation_base, module_size);
                return false;
            }
        }
    }
    
    // 如果PE头检查失败，但内存是可执行的，我们仍然可以认为它有效
    dbg_log!("PE头检查失败，但地址 {:p} 可执行，认为有效", address);
    true
}

/// 使用HDE64反汇编器查找返回指令
pub unsafe fn find_return_instruction(code: *const u8, max_length: usize) -> *mut c_void {
    if code.is_null() || max_length == 0 {
        return null_mut();
    }
    let mut offset: usize = 0;
    let mut hs = HDE64::default();

    while offset < max_length {
        let current_code = code.add(offset);
        let len = hde64_disasm(current_code, &mut hs, max_length - offset);
        if len == 0 || len > 15 {
            break;
        }
        // RET (C3, C2), RETF (CB, CA)
        if matches!(hs.opcode, 0xC2 | 0xC3 | 0xCA | 0xCB) {
            dbg_log!("在偏移 {} 处找到返回指令 (0x{:02X})", offset, hs.opcode);
            return current_code as *mut c_void;
        }
        offset += len as usize;
    }
    null_mut()
}


/// 查找函数序言的结束位置
pub unsafe fn find_function_prologue_end(function_address: *mut c_void) -> Option<*mut c_void> {
    let Some(gadget) = get_gadget() else {
        error_log!("查找函数序言失败: 无可用Gadget");
        return None;
    };

    let rtl_lookup_addr = get_function_address(NTDLL, RTL_LOOKUP_FUNCTION_ENTRY.as_bytes());
    if rtl_lookup_addr.is_null() {
        dbg_log!("无法定位 RtlLookupFunctionEntry");
        return None;
    }
    
    let mut image_base: u64 = 0;
    let runtime_function = CallR12(
        rtl_lookup_addr,
        3,
        gadget,
        function_address as u64 as *mut c_void,
        &mut image_base as *mut u64 as *mut c_void,
        null_mut::<c_void>(),
        null_mut::<c_void>(),
        null_mut::<c_void>(),
        null_mut::<c_void>(),
    ) as *mut crate::RUNTIME_FUNCTION;

    if runtime_function.is_null() || image_base == 0 {
        dbg_log!("RtlLookupFunctionEntry failed for {:p}", function_address);
        return None;
    }
    
    let unwind_info_rva = (*runtime_function).UnwindInfoAddress;
    let unwind_info = (image_base as usize + unwind_info_rva as usize) as *const UNWIND_INFO;

    let size_of_prolog = (*unwind_info).SizeOfProlog;
    let begin_address = (*runtime_function).BeginAddress;

    let prologue_end = (image_base + begin_address as u64 + size_of_prolog as u64) as *mut c_void;
    dbg_log!("函数序言结束于 {:p} (偏移 {})", prologue_end, size_of_prolog);

    Some(prologue_end)
}


/// **[主函数]** 查找函数的返回地址
pub unsafe fn find_return_address(function_address: *mut c_void) -> *mut c_void {
    if function_address.is_null() {
        return null_mut();
    }
    info_log!("开始为函数 {:p} 查找返回地址", function_address);

    // 1. 获取函数的基本信息 (模块基址, 函数大小等)
    let (module_base, function_size) =
        if let Some(mbi) = query_virtual_memory_with_retry(function_address) {
            let base = mbi.AllocationBase;
            // 尝试通过RtlLookupFunctionEntry获取更精确的函数大小
            let size = get_function_size(function_address).unwrap_or(5000); // 默认扫描5000字节
            (base, size)
        } else {
            (null_mut(), 5000) // Fallback
        };

    // 2. 方法一：使用异常处理信息 (Unwind Info)
    let mut unwind_method_result = null_mut();
    if let Some(prologue_end) = find_function_prologue_end(function_address) {
        let scan_start = prologue_end as *const u8;
        let scan_len = function_size.saturating_sub(prologue_end as usize - function_address as usize);
        if let Some(ret_addr) = find_return_instruction(scan_start, scan_len).as_mut() {
            if is_valid_executable_address(ret_addr, module_base) {
                info_log!("方法一 (Unwind Info) 找到有效返回地址: {:p}", ret_addr);
                unwind_method_result = ret_addr;
            }
        }
    }

    // 3. 方法二：直接从函数头开始扫描 (HDE64)
    let mut hde64_method_result = null_mut();
    if let Some(ret_addr) = find_return_instruction(function_address as *const u8, function_size).as_mut() {
        if is_valid_executable_address(ret_addr, module_base) {
            info_log!("方法二 (HDE64 Scan) 找到有效返回地址: {:p}", ret_addr);
            hde64_method_result = ret_addr;
        }
    }

    // 4. 结果决策
    if !unwind_method_result.is_null() {
        if unwind_method_result == hde64_method_result {
            info_log!("✅ 两种方法结果一致，返回 {:p}", unwind_method_result);
        } else {
            dbg_log!("⚠️ 两种方法结果不一致，优先采用 Unwind Info 方法结果: {:p}", unwind_method_result);
        }
        return unwind_method_result; // 优先相信Unwind Info
    }
    if !hde64_method_result.is_null() {
        info_log!("仅 HDE64 扫描方法成功，返回 {:p}", hde64_method_result);
        return hde64_method_result;
    }

    // 5. 最终回退：简单模式匹配
    // 在所有智能方法失败后，进行最后一次暴力扫描，只检查可读性
    dbg_log!("所有方法均失败，尝试最后一次暴力扫描...");
    let ret_addr = find_return_instruction(function_address as *const u8, function_size);
    if !ret_addr.is_null() {
         let is_readable = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
            let _ = *(ret_addr as *const u8);
        })).is_ok();
        if is_readable {
            dbg_log!("找到一个可读的返回地址(未经验证): {:p}", ret_addr);
            return ret_addr;
        }
    }


    error_log!("所有方法均失败，未能找到 {:p} 的返回地址", function_address);
    null_mut()
}

// 辅助函数，获取函数大小
unsafe fn get_function_size(function_address: *mut c_void) -> Option<usize> {
    let Some(gadget) = get_gadget() else { return None; };
    let rtl_lookup_addr = get_function_address(NTDLL, RTL_LOOKUP_FUNCTION_ENTRY.as_bytes());
    if rtl_lookup_addr.is_null() { return None; }
    
    let mut image_base: u64 = 0;
    let runtime_function = CallR12(
        rtl_lookup_addr, 3, gadget,
        function_address as u64 as *mut c_void,
        &mut image_base as *mut u64 as *mut c_void,
        null_mut::<c_void>(),
        null_mut::<c_void>(),
        null_mut::<c_void>(),
        null_mut::<c_void>(),
    ) as *const crate::RUNTIME_FUNCTION;

    if !runtime_function.is_null() {
        let size = (*runtime_function).EndAddress - (*runtime_function).BeginAddress;
        return Some(size as usize);
    }
    None
}
